const path = require('path')
// const SentryPlugin = require('@sentry/webpack-plugin')
const VConsolePlugin = require('vconsole-webpack-plugin')
const UglifyJsPlugin = require('uglifyjs-webpack-plugin')
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
const LodashWebpackPlugin = require('lodash-webpack-plugin')
const CompressionPlugin = require("compression-webpack-plugin");
const webpack = require('webpack')
const version = require('./package.json').version

const {
  VUE_APP_TITLE,
  DEVSERVERPORT,
  NODE_ENV,
  VCONSOLE,
  VUE_APP_SENTRY_ENABLED,
  VUE_APP_SENTRY_PLUGIN_ENABLED
} = process.env

const resolve = dir => path.join(__dirname, dir)
const DEV = NODE_ENV === 'development'
const PROD = NODE_ENV === 'production'


module.exports = {
  transpileDependencies: ['idb'],
  publicPath: !DEV ? '/' : '/',
  outputDir: 'dist',
  assetsDir: 'static',
  lintOnSave: DEV,
  productionSourceMap: PROD && VUE_APP_SENTRY_ENABLED === 'yes' && VUE_APP_SENTRY_PLUGIN_ENABLED === 'yes',
  devServer: {
    port: Number(DEVSERVERPORT),
    open: true,
    overlay: {
      warnings: false,
      errors: true
    }
  },
  // pwa: {
  //   name: VUE_APP_TITLE,
  //   workboxPluginMode: 'InjectManifest',
  //   workboxOptions: {
  //     swSrc: resolve('src/pwa/service-worker.js')
  //   }
  // },
  // configureWebpack: {
  //   name: VUE_APP_TITLE,
  //   resolve: {
  //     alias: {
  //       '@': resolve('src')
  //     }
  //   }
  // },
  configureWebpack: config => {
    config.entry.app = ["babel-polyfill", "./src/main.js"];
  },
  pluginOptions: {
    'style-resources-loader': {
      preProcessor: 'scss',
      patterns: [
        resolve('src/styles/_variables.scss'),
        resolve('src/styles/_mixins.scss')
      ]
    }
  },
  chainWebpack(config) {
    config.plugins.delete('preload')
    config.plugins.delete('prefetch')

    config.plugin('__VERSION__')
      .use(new webpack.DefinePlugin({
        __VERSION__: JSON.stringify(version)
      }))
      .end()
      if (!DEV) {
      config.plugin('compressionPlugin')
        .use(new CompressionPlugin({
          test: /\.js$|\.html$|\.css$|\.jpg$|\.jpeg$|\.png/, // 需要压缩的文件类型
          threshold: 10240, // 归档需要进行压缩的文件大小最小值，我这个是10K以上的进行压缩
          deleteOriginalAssets: false // 是否删除原文件
        }))
        .end()
      }
      // config.plugin('bundleAnalyzerPlugin')
      //   .use(new BundleAnalyzerPlugin())
      //   .end()

    if (!DEV) {
      config.plugin('loadshReplace')
        .use(new LodashWebpackPlugin())
        .end()
      // if (VUE_APP_SENTRY_PLUGIN_ENABLED === 'yes') {
      //   config.plugin('uglifyjs-webpack-plugin')
      //     .use(new UglifyJsPlugin({
      //       uglifyOptions: {
      //         compress: {
      //           drop_debugger: true,
      //           drop_console: true
      //         },
      //         safari10: true
      //       },
      //       sourceMap: false,
      //       parallel: true
      //     }))
      //     .end()
      // }
    }
    config.plugin('VConsolePlugin')
      .use(new VConsolePlugin({
        filter: [],
        enable: DEV && VCONSOLE === 'yes'
      }))
      .end()

    config.plugin('ProvidePlugin')
      .use(new webpack.ProvidePlugin({
        _: 'lodash'
      }))
      .end()

    // set svg-sprite-loader
    config.module
      .rule('svg')
      .exclude.add(resolve('src/icons'))
      .end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()

    // set preserveWhitespace
    config.module
      .rule('vue')
      .use('vue-loader')
      .loader('vue-loader')
      .tap(options => {
        options.compilerOptions.preserveWhitespace = true
        return options
      })
      .end()

    config
      .when(DEV, config => config.devtool('cheap-source-map'))

    config
      .when(!DEV,
        config => {
          // config
          //   .plugin('ScriptExtHtmlWebpackPlugin')
          //   .after('html')
          //   .use('script-ext-html-webpack-plugin', [{
          //     inline: /runtime\..*\.js$/
          //   }])
          //   .end()
          config
            .optimization.splitChunks({
              chunks: 'all',
              cacheGroups: {
                libs: {
                  name: 'chunk-libs',
                  test: /[\\/]node_modules[\\/]/,
                  priority: 10,
                  chunks: 'initial'
                },
                elementUI: {
                  name: 'chunk-vantUI',
                  priority: 20,
                  test: /[\\/]node_modules[\\/]_?vant(.*)/
                },
                commons: {
                  name: 'chunk-commons',
                  test: resolve('src/components'),
                  minChunks: 3,
                  priority: 5,
                  reuseExistingChunk: true
                }
              }
            })
          config.optimization.runtimeChunk('single')
        }
      )

    // if (!DEV) {
    //   if (PROD && VUE_APP_SENTRY_ENABLED === 'yes' && VUE_APP_SENTRY_PLUGIN_ENABLED === 'yes') {
    //     config.plugin('sentryPlugin')
    //       .use(new SentryPlugin({
    //         release: version,
    //         include: path.join(__dirname, './dist/static/js'),
    //         urlPrefix: '~/static/js',
    //         ignore: ['node_modules']
    //       }))
    //       .end()
    //   }
    // }
  }
}
