<template>
  <div>
    <div style="position:relative; z-index:-2;">
      <van-image class="headerImg" :src="require('../../assets/qr-share.jpg')" style="position:absolute; z-index:0; width:100%" />
    </div>
    <div style="padding-top: 50%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;">
      <qriously style="background-color: white; width:200px" :value="qrcode" :size="200" />
      <div style="padding-top:30px">*请截屏分享二维码或直接扫码邀请</div>
    </div>
    
  </div>
</template>

<script>
import { readFromLocalStorage } from '@/core/services/cache'
export default {
  name: 'Qrcode',
  props: {},
  data() {
    return {
      qrcode: ''
    }
  },
  created() {
    let info = readFromLocalStorage('info')
    this.qrcode = `${process.env.VUE_APP_BASE_URL}/inviteDoctor?url=${info.promotion_url}&wd=${info.wd}`
  },
  mounted() {},
  methods: {}
}
</script>

<style lang="scss" scoped>
</style>
