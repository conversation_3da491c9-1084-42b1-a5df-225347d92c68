<template>
  <div>
    <div style="position : relative;padding-bottom: 42px;">
      <van-image v-if="wd == 0" fit="fill" :src="require('../../assets/invite_0.jpg')" style="z-index:0;width:100%" />
      <van-image v-if="wd == 1" fit="fill" :src="require('../../assets/invite_1.jpg')" style="z-index:0;width:100%" />
    </div>
    <footer style="width:100%;position:fixed;bottom:0;left:0;height:42px;background-color:white">
      <van-button color="#ff9000" style="width: 100%" @click="jumpToUrl">接受邀请并注册</van-button>
    </footer>
  </div>
</template>

<script>
export default {
  name: 'InviteDoctor',
  props: {},
  data() {
    return {
      url: '',
      wd: 0
    }
  },
  created() {
    let url = this.$route.query.url
    this.url = url
    if (this.$route.query.wd !== undefined) {
      this.wd = this.$route.query.wd
    }
    
  },
  mounted() {},
  methods: {
    jumpToUrl(){
      window.location.href = this.url
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
