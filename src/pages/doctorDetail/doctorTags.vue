<template>
  <div style="background-color:white;">
    <div style="padding: 10px">已添加标签（{{doctorTagsList.length}}/5）</div>
    <div>
      <div style="margin: 0 10px; background-color:#f3f3f3;height:100px">
        <div style="display:flex;flex-flow: wrap;">
        <div v-for="(item, index) in doctorTagsList" :key="index" style="padding: 8px;"  >
          <van-tag v-if="item.selected" closeable round type="primary" size="medium" color="#ff9000" @close="selectTagAction(item, 0)">{{item.name}}</van-tag>
        </div>
      </div>
      </div>
    </div>
    <div style="padding: 10px">标签</div>
    <div style="display:flex;flex-flow: wrap;">
      <div v-for="(item, index) in tagsList" :key="index" style="padding: 8px;" @click="selectTagAction(item, 1)">
        <van-tag round size="medium" :color="item.selected ? '#ff9000' : '#cecaca' ">{{item.name}}</van-tag>
      </div>
      <div style="padding: 8px;">
        <van-tag round plain type="primary" size="medium" @click="showDialog = true">自定义标签</van-tag>
      </div>
    </div>
    <van-dialog v-model="showDialog" title="新增标签" show-cancel-button confirmButtonText="保存" confirmButtonColor="#ff9000" @confirm="confirmTagName">
      <van-field v-model="tagName" label="" placeholder="请输入标签内容，少于6个字" />
    </van-dialog>
  </div>
</template>

<script>
import { doctorInteractor } from '@/core'
import qs from 'qs'
import dayjs from 'dayjs'
import quarterOfYear from 'dayjs/plugin/quarterOfYear' // ES 2015
import 'dayjs/locale/zh-cn' // ES 2015 
import { Toast } from 'vant'

dayjs.locale('zh-cn')
dayjs.extend(quarterOfYear)

export default {
  name: 'DoctorTags',
  props: {},
  data() {
    return {
      doctorTagsList: [],
      tagsList: [],
      showDialog: false,
      tagName: undefined
    }
  },
  created() {
    this.doctor_id = this.$route.query.doctor_id
  },
  mounted() {
    this.fetchGetPromoterTag()
    console.log('mounted')
  },
  methods: {
    async fetchGetPromoterTag(){
      try {
        await doctorInteractor.fetchGetPromoterTag({doctor_id: this.doctor_id}).then(data => {
          this.tagsList = data.tag_list
          let array = []
          this.tagsList.map(item => {
          if (item.selected) {
              array.push(item)
            }
          })
          this.doctorTagsList = array
        })
      } catch (error) {
        console.log(error)
      }
    },
    selectTagAction(item, type) {
      console.log('selectTagAction')
      let dict = { doctor_id: this.doctor_id, tag_array: [] }
      let tagArray = []
      this.tagsList.map(item => {
        if (item.selected) {
          tagArray.push({ id: item.id, tag_type: item.tag_type})
        }
      })
      if (type == 1) {
        tagArray.push({ 'id': item.id, 'tag_type': item.tag_type })
      } else {
        tagArray.map((element, index) => {
                if (element.id === item.id) {
                    tagArray.splice(index, 1)
                }
            })
      }
      
      dict['tag_array'] = tagArray
      doctorInteractor.fetchSetPromoterDoctorTag(dict).then(data => {
          console.log(data)
          this.fetchGetPromoterTag()
      })
    },
    confirmTagName() {
      let dict = { name : this.tagName}
      doctorInteractor.fetchSetPromoterTag(dict).then(data => {
          console.log(data)
          this.fetchGetPromoterTag()
          this.tagName = undefined
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.cell {
  padding: 8px 0 0 0 ;
}
::v-deep .van-cell-group--inset {
  margin: 0 8px;
}
</style>
