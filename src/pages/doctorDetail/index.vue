<template>
  <div>
    <!-- <div style="position:relative; z-index:0;">
      <div class="header-view"></div>
    </div> -->
    <footer style="width:100%;position:fixed;bottom:0;left:0;height:60px;z-index: 10;">
      <div style="background-color: rgb(255 144 0 / 63%);width: 50px;height: 50px;align-items: center;display: flex;justify-content: center;position: absolute;right: 10px;bottom: 10px;border-radius: 25px;" @click="jumpToFlollow()">
        <van-icon name="edit" size="30"  color="white"/>
      </div>
    </footer>
    <div style="background-color: white;">
      
      <div v-if="doctor" style="padding: 10px;display: flex; flex-direction: row; align-items: center;justify-content: space-between;">
        <div style="display:flex;align-items: center;width: 100%;">
          <div style="width: 70px;" v-if="doctor">
            <AvatarButton :url="doctor.credential.figure_file ? doctor.credential.figure_file.url : ''" :statusString="doctor.credential.status_name" :status="doctor.credential.status"/>
          </div>
          <div style="display:flex;flex-direction: column;width: 100%;">
            <div style="display: flex;padding-top: 10px; width: 100%;align-items: center;justify-content: space-between;">
              <div style="display:flex;align-items: center;">
                <div style="font-size: 16px;font-weight: bold;">{{doctor.name}}</div>
                <div style="font-size: 14px;padding-left:6px; color: gray;">{{ doctor.credential.professional_title }}</div>
              </div>
              <div v-if="doctor_extend.birthday" style="color: #f39000; font-size: 14px;display: flex;align-items: center;margin-left: 10px;">
                <van-icon name="point-gift-o" size="18" />
                <div >{{ doctor_extend.birthday }}</div>
              </div>
            </div>
            <div style="display: flex;padding-top: 10px; width: 100%;align-items: center;justify-content: space-between;">
              <div style="display:flex;align-items: center; color: gray;">
                <div style="font-size: 14px;">{{doctor_extend.hospital_areas}}</div>
                <div style="font-size: 14px;padding-left:6px; color: gray;">{{doctor_extend.hospital_name}}</div>
              </div>
            </div>
            <!-- <div style="display: flex;padding-top: 10px; width: 100%;align-items: center;justify-content: space-between;">
              <div style="font-size: 14px;">医生归属：{{referee_promoter_name}}</div>
              <div style="display: flex;" @click="jumpToInfo()">
                <div style="font-size: 14px;font-weight: normal;color: #2781D7;">详细资料</div>
                <van-icon name="arrow" />
              </div>
            </div> -->
          </div>
        </div>
      </div>
      <van-divider dashed style="margin: 0;"></van-divider>
      <div v-if="doctor" style="display: flex;padding: 10px 20px; width: 100%;align-items: center;justify-content: space-between;">
        <div style="font-size: 14px;display: flex;">
          <div>对接人:{{referee_promoter_name}}</div>
          <div style="font-size: 14px;padding-left:10px;">电话:{{doctor_extend.mobile}}</div>
        
        </div>
        
        <div style="display: flex;" @click="jumpToInfo()">
          <div style="font-size: 14px;font-weight: bold;color: #2781D7;">详细资料</div>
        </div>
      </div>
    </div>
    <van-popup v-if="doctor" v-model="showDoctorBirthdayDatePicker" position="center" style="width: 80%">
      
      <van-datetime-picker
      :title="doctor.name + '医生的生日'"
        v-model="currentDoctorBirthdayDate"
        type="date"
        :formatter="formatter"
        :min-date="doctorBirthdayMinDate"
        :max-date="maxDate"
        @confirm="doctorBirthdaydateOnConfirm"
        @cancel="showDoctorBirthdayDatePicker = false"
      />
    </van-popup>
    <!-- 标签栏 -->
    <div class="line-button">
      <van-tabs 
        v-model="active"
        ref="mtabs"
        color="#ff9000" title-active-color="#ff9000"
        background="white">
        <template #nav-right>
          <div>
            <van-button @click="showPopup = true">选择日期</van-button>
          </div>
        </template>
        <van-tab title="处方记录" name="a" >
          <!-- 列表 -->
          <van-pull-refresh v-model="loading" @refresh="onRefresh" class="list-container">
            <van-list
              v-model="loading"
              :finished="finished"
              finished-text="没有更多了"
              @load="onLoad"
              offset="150"
            >
              <van-index-bar class="indexBar"  :sticky="false" highlight-color="#fb6463">
                  <van-index-anchor v-for="(hitem,index) in headerList" :key="index" :index="index">
                    <span class="indexWord">{{hitem}}</span>
                    <van-panel :title="('日期：' + item.created_at)"  :status="('状态：' + item.shipping_status)" v-for="item in list[index]" :key="item.no">
                      <van-grid  :column-num="2">
                        <!-- <van-grid-item :text="'问诊单号：' + item.no" /> -->
                        <van-grid-item :text="'订单单号：' + item.order_no" />
                        <van-grid-item v-if="item.express_no" >
                          <div style="display: flex;font-size: 12px;">
                            <div>快递单号：</div>
                            <a style="color:blue" @click="showExpressAction(item)">{{item.express_no}}</a>
                          </div>
                        </van-grid-item>
                        <!-- <van-grid-item :text="'问诊单金额：' + item.visits_money" /> -->
                        <van-grid-item :text="(pharmacy_type == 2 ? '订单金额：' : '基础药费：') + item.medicine_charge" />
                        <!-- <van-grid-item :text="'处方单金额：' + item.amount" /> -->
                      </van-grid>
                    </van-panel>
                  </van-index-anchor>
              </van-index-bar>
            </van-list>
          </van-pull-refresh>
        </van-tab>
        <van-tab title="数据统计" name="b" >
          <div class="mid-card" style="margin-top: 20px">
            <div style="padding-top: 10px">
              <van-row type="flex" justify="space-around" v-if="counts">
                <van-col span="11"><DoctorStatisticsTitle title="订单笔数" :count="counts.order_number"/></van-col>
                <van-col span="11"><DoctorStatisticsTitle title="订单金额" :count="counts.order_total_amount"/></van-col>
              </van-row>
            </div>
            <div>
              <van-cell style="margin-top:10px" title="药费金额" value-class="cell-value" :value="'¥' + counts.base_medicine_charge" v-if="counts && pInfo && pInfo.promoter_type == 1"/>
              <van-cell title="纯销金额" value-class="cell-value" :value="'¥' + counts.order_amount" v-if="counts && pInfo  && pInfo.promoter_type == 1"/>
              <van-cell title="奖金预估" value-class="cell-value" :value="'¥' + counts.bonus_predict" v-if="counts && pInfo && pInfo.promoter_type == 1"/>
            </div>
          </div>
        </van-tab>
         <van-tab title="跟进记录" name="c" >
          
          <van-pull-refresh v-model="followListLoading" @refresh="followListOnRefresh" class="list-container">
            <van-list
              v-model="followListLoading"
              :finished="followListFinished"
              finished-text="没有更多了"
              @load="followListOnLoad"
              offset="150"
            >
            <div v-for="(item, index) in promoterFollowList" :key="index" style="position: relative;padding-top:10px;border-radius:5px;overflow:hidden;">
              <div style="display: flex;text-align: center;margin: 6px;align-items: center;">
                <van-icon name="circle" size="12" color="#ff9000"/>
                <div style="font-size:14px;padding-left:4px">{{item.date}}</div>
              </div>
              <div style="position: absolute;top: 28px;left: 11px;height: 90%;background-color: lightgray;width: 1.5px;z-index: 0;"></div>
              <div v-for="(litem, lindex) in item.list" :key="lindex" style="position: relative;background-color: white;z-index: 2;margin:6px;padding:10px 8px;font-size:14px;">
                <div style="display: flex;justify-content: space-between;">
                  <div style="display: flex;">
                    <div>{{litem.promoter_name}}</div>
                    <div style="color:gray;padding-left:10px">{{litem.promoter_level}}</div>
                  </div>
                  <div>{{litem.follow_type_str}}</div>
                </div>
                <div style="padding: 10px 0;color:gray">{{litem.content}}</div>
                <div style="display: flex;justify-content: space-between;color:gray">
                  <div style="display: flex;">
                    <van-icon name="clock-o" />
                    <div>{{litem.created_at}}</div>
                  </div>
                  <div style="color:red;" @click="deletePromoterFollow(litem.id)">删除</div>
                </div>
              </div>
            </div>
            </van-list>
          </van-pull-refresh>
        </van-tab>
      </van-tabs>
    </div>
    
    <van-popup
        v-model="showPopup"
        position="top"
        :close-on-click-overlay = false
        >
        <van-form>
          <van-field
            readonly
            clickable
            name="picker"
            :value="query.begin_time ? query.begin_time.toLocaleDateString() : ''"
            label="开始时间"
            placeholder="点击选择开始时间"
            @click="showPopupBeginDatePicker = true"
          />
          <van-field
            readonly
            clickable
            name="picker1"
            :value="query.end_time ? query.end_time.toLocaleDateString() : ''"
            label="结束时间"
            placeholder="点击选择结束时间"
            @click="showPopupEndDatePicker = true"
          />
        </van-form>
        <div>
          <van-row>
            <van-col span="12">
              <van-button class="width-button" color="#F7941E" type="primary" @click="filterAction">确定</van-button>
            </van-col>
            <van-col span="12">
              <van-button class="width-button" color="#D4D4D4" type="primary" @click="cleanFilterAction">清除条件</van-button>
            </van-col>
          </van-row>
        </div>
      </van-popup>
    <van-popup
        v-model="showPopupBeginDatePicker"
        position="bottom"
      >
        <van-datetime-picker
          v-model="begin_time"
          type="date"
          :formatter="formatter"
          :min-date="minDate"
          :max-date="maxDate"
          @confirm="confirmPopupBeginDatePicker"
          @cancel="showPopupBeginDatePicker = false"
        />
      </van-popup>
      <van-popup
        v-model="showPopupEndDatePicker"
        position="bottom"
      >
        <van-datetime-picker
          v-model="end_time"
          type="date"
          :formatter="formatter"
          :min-date="query.begin_time ? query.begin_time : minDate"
          :max-date="maxDate"
          @confirm="confirmPopupEndDatePicker"
          @cancel="showPopupEndDatePicker = false"
        />
      </van-popup>
      <van-popup v-model="showExpress" round position="center" :closeable=true :style="{ height: '80%', width: '80%' }">
        <van-steps direction="vertical" :active="0">
          <van-step v-for="(item, index) in expressSteps" :key="index" :index="index">
            <h4>{{item.context}}</h4>
            <p>{{item.time}}</p>
          </van-step>
        </van-steps>
      </van-popup>
  </div>
</template>

<script>
import { doctorInteractor } from '@/core'
import AvatarButton from '@/components/AvatarButton'
import DoctorStatisticsTitle from '@/components/DoctorStatisticsTitle'
import { Toast, Dialog } from 'vant'
import { readFromLocalStorage } from '@/core/services/cache'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn' // ES 2015 

dayjs.locale('zh-cn')
export default {
  name: 'DoctorDetail',
  props: {},
  components: {
    AvatarButton, DoctorStatisticsTitle
  },
  data() {
    return {
      doctor: undefined,
      doctor_extend: undefined,
      pInfo: undefined,
      headerList: [],
      list: [],
      active: 0,
      query: {
        page: 0,
        size: 8,
        doctor_id: undefined,
        begin_time: undefined,
        end_time: undefined 
      },
      pharmacy_type: 0,
      begin_time: undefined,
      end_time: undefined,
      loading: false,
      finished: false,
      showPopup: false,
      currentDoctorBirthdayDate: new Date(1980, 0, 1),
      doctorBirthdayMinDate: new Date(1950, 0, 1),
      showDoctorBirthdayDatePicker: false,
      minDate: new Date(2019, 0, 1),
      maxDate: new Date(),
      showPopupBeginDatePicker: false,
      showPopupEndDatePicker: false,
      showExpress: false,
      expressSteps: [],
      counts: undefined,
      followListLoading: false,
      followListFinished: false,
      followListQuery: {
        page: 0,
        size: 8,
        doctor_id: undefined,
        begin_time: undefined,
        end_time: undefined 
      },
      promoterFollowList: [],
      tagsList:[],
      referee_promoter_name: undefined
    }
  },
  filters: {
    statusStringFilter(status) {
      const statusMap = {
        0: '未认证',
        1: '已认证',
        2: '已拒绝',
        3: '停用',
      }
      return statusMap[status]
    },
  },
  created() {
    let pInfo = readFromLocalStorage('info')
    console.log(pInfo)
    if (pInfo) {
      this.pInfo = pInfo
    }
    this.query.doctor_id = this.$route.query.doctor_id
    this.followListQuery.doctor_id = this.$route.query.doctor_id
    // this.getDoctorInfo(this.query)

    // this.fetchGetPromoterFollowList()
    
  },
  mounted() {
    this.fetchGetPromoterDoctorTag()
    this.$bus.$on("selectedFollow", (r) => {
      console.log('selectedFollow')
      this.active = 2
    });
  },
  methods: {
    jumpToInfo() {
      this.$router.push({path:'/doctorEditInfo',query: { doctor_id : this.$route.query.doctor_id}})
    },
    formatter(type, val) {
      if (type === 'year') {
        return `${val}年`;
      } else if (type === 'month') {
        return `${val}月`
      } else {
        return `${val}日`
      }
      return val;
    },
    doctorBirthdaydateOnConfirm(v) {
      this.showDoctorBirthdayDatePicker = false;
      this.fetchEditDoctorBirthday(v)
    },
    async fetchEditDoctorBirthday(d) {
      let ds = dayjs(d).format('YYYY-MM-DD')
      try {
        await doctorInteractor.fetchEditDoctorBirthday({birthday: ds, doctor_id: this.query.doctor_id}).then(data => {
          this.onRefresh()
        })
      } catch (error) {
        console.log(error)
      }
    },
    jumpToFlollow() {
      this.$router.push({path:'/follow',query: { doctor_id : this.query.doctor_id, doctor_name: this.doctor.name}})
    },
    jumpToTags() {
      this.$router.push({path:'/doctorTags',query: { doctor_id : this.query.doctor_id}})
    },
    async fetchGetPromoterDoctorTag(){
      try {
        await doctorInteractor.fetchGetPromoterDoctorTag({doctor_id: this.query.doctor_id}).then(data => {
          this.tagsList = data.tag_list
        })
      } catch (error) {
        console.log(error)
      }
    },
    confirmPopupBeginDatePicker(t) {
      this.query.begin_time = t
      this.showPopupBeginDatePicker = false
    },
    confirmPopupEndDatePicker(t) {
      this.query.end_time = t
      this.showPopupEndDatePicker = false
    },
    filterAction() {
      this.showPopup = false
      this.query.page = 1
      this.headerList = []
      this.list = []
      this.finished = false
      this.active = 'a'
      this.getDoctorInfo(this.query)
    },
    cleanFilterAction() {
      this.query.end_time = undefined
      this.query.begin_time = undefined
      this.showPopup = false
      this.list = []
      this.headerList = []
      this.query.page = 1
      this.active = 'a'
      console.log(this.query)
      this.getDoctorInfo(this.query)
    },
    showExpressAction(item) {
      if (item.logistics) {
        this.expressSteps = []
        this.expressSteps = item.logistics.data
        this.showExpress = true
      } else {
        Toast.fail('快递小哥正在揽收途中')
      }
      
    },
    async getDoctorInfo(query) {
      try {
        let q = Object.assign({}, this.query, query)
        if (this.query.begin_time) {
          q.begin_time = dayjs(this.query.begin_time).format('YYYY-MM-DD')
          q.end_time = dayjs(this.query.end_time).format('YYYY-MM-DD')
        }
        const data = await doctorInteractor.fetchDoctorInfo(q).then(data => {
          this.loading = false
          this.counts = data.report
          this.doctor = data.doctor_info
          this.doctor_extend = data.doctor_extend
          this.referee_promoter_name = data.referee_promoter_name
          data.list.forEach(element => {
            this.pharmacy_type = element.pharmacy_type
            if (this.headerList.indexOf(element.month) > -1) {
              let index = this.headerList.indexOf(element.month)
              let a = this.list[index]
              a.push(element)
            } else {
              this.headerList.push(element.month)
              let b = []
              b.push(element)
              this.list.push(b)
            }
          })
          let total = 0
          this.list.forEach( a => {
            total = total + a.length
          })
          if (total === data.total) {
            this.finished = true
          } else {
            this.finished = false
          }

        })
        // this.listTotal = total
      } catch (error) {
        console.log(error)
      }
    },
    onLoad() {
      // 异步更新数据
      // setTimeout 仅做示例，真实场景中一般为 ajax 请求
      this.loading = true
      this.query.page = this.query.page + 1
      console.log('onLoad')
      this.getDoctorInfo(this.query)
    },
    onRefresh() {
      // 清空列表数据
      this.finished = false
      console.log('onRefresh')
      // 重新加载数据
      // 将 loading 设置为 true，表示处于加载状态
      this.loading = true
      this.query.page = 1
      this.headerList = []
      this.list = []
      this.getDoctorInfo(this.query)
    },
    followListOnLoad() {
      // 异步更新数据
      // setTimeout 仅做示例，真实场景中一般为 ajax 请求
      this.followListLoading = true
      this.followListQuery.page = this.followListQuery.page + 1
      console.log('onLoad')
      this.fetchGetPromoterFollowList(this.followListQuery)
    },
    followListOnRefresh() {
      // 清空列表数据
      this.followListFinished = false
      console.log('onRefresh')
      // 重新加载数据
      // 将 loading 设置为 true，表示处于加载状态
      this.followListLoading = true
      this.followListQuery.page = 1
      this.promoterFollowList = []
      this.fetchGetPromoterFollowList(this.followListQuery)
    },
    async fetchGetPromoterFollowList(query) {
      try {
        let q = Object.assign({}, this.followListQuery, query)
        if (this.followListQuery.begin_time) {
          q.begin_time = dayjs(this.followListQuery.begin_time).format('YYYY-MM-DD')
          q.end_time = dayjs(this.followListQuery.end_time).format('YYYY-MM-DD')
        }
        const data = await doctorInteractor.fetchGetPromoterFollowList(q).then(data => {

          this.followListLoading = false
          data.follow_list.forEach(element => {
            this.promoterFollowList.push(element)
          })
          
       
          let count = 0
          this.promoterFollowList.map(item => {
            count = count + item.list.length
          })

          if (count >= data.total) {
            this.followListFinished = true
          } else {
            this.followListFinished = false
          }

        })
      } catch (error) {
        console.log(error)
      }
    },
    deletePromoterFollow(id) {
      Dialog.confirm({
        title: '温馨提醒',
        message: '是否删除跟进记录',
      })
        .then(() => {
          // on confirm
          doctorInteractor.deletePromoterFollow({follow_list_id: id}).then(data => {
            this.followListOnRefresh()
          })
        })
        .catch(() => {
          // on cancel
        });
      
    }

  }
}
</script>

<style lang="scss" scoped>
::v-deep .van-tabs{
  width: 100%;
}

.mid-card {
  margin: 0px -6% 0px 6%;
  width: 88%;
  background-color: white;
  border-radius: 5px;
}
.header-view {
  position:absolute; 
  z-index:2;
  height: 100px;
  width: 100%;
  // background-image: linear-gradient(to bottom, #F7941E, #FFB966);
}
.line-button {
  display: flex;
  justify-content: space-between;
  // background-color: white;
  margin-top: 0px;
}
.right-label {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
::v-deep .van-index-anchor {
  padding: 0px;
}
::v-deep .van-cell {
  padding: 8px 8px;
}
::v-deep .van-grid-item__content {
  padding: 2px 4px;
}

</style>
