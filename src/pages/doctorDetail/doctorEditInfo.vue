<template>
  <div>
    <div style="">
      
      <div v-if="doctor" style="background-color: white;padding: 10px;display: flex; flex-direction: row; align-items: center;justify-content: space-between;">
        <div style="display:flex;align-items: center;">
          <div style="width: 70px;" v-if="doctor">
            <AvatarButton :url="doctor.credential.figure_file ? doctor.credential.figure_file.url : ''" :statusString="doctor.credential.status_name" :status="doctor.credential.status"/>
          </div>
          <div style="display:flex;flex-direction: column;justify-content: center;">
            <div style="display:flex;align-items: center;">
              <div style="font-size: 16px;font-weight: bold;">{{doctor.name}}</div>
              <div style="font-size: 14px;padding-left:6px; color: gray;">{{doctor.credential.professional_title}}</div>
            </div>
            <div style="display: flex;padding-top: 10px; width: 100%;align-items: center;justify-content: space-between;">
              <div style="display:flex;align-items: center; color: gray;">
                <div style="font-size: 14px;">{{doctor_extend.hospital_areas}}</div>
                <div style="font-size: 14px;padding-left:6px; color: gray;">{{doctor_extend.hospital_name}}</div>
              </div>
            </div>
          </div>

        </div>
      </div>
      <div v-if="extraDoctorInfo">
        <div style="margin-top: 10px;"></div>
        <van-cell :title-style="{fontSize: '16px'}" title="手机" :value="doctor_extend.mobile"></van-cell>
        <van-cell :title-style="{fontSize: '16px'}" title="对接人" :value="referee_promoter_name"></van-cell>
        <van-field name="birthday" input-align="right" :value="doctor.birthday" placeholder="请输入医生生日信息" @click="showDoctorBirthdayAction()">
          <template #label>
            <div style="font-size: 16px;color: #373637;">生日</div>
          </template>
        </van-field>
        <van-field label-width="100" name="outpatient_volume" input-align="right" placeholder="请输入门诊量" @click="openAllDialog(1)">
          <template #label>
            <div style="display: flex;">
              <div style="font-size: 16px;color: #373637;">门诊量</div>
              <div>
                <van-tag round style="margin-left: 6px;" :color=" extraDoctorInfo.outpatient_volume.status | statusColorFilter " plain v-if="extraDoctorInfo.outpatient_volume.status > -1 && extraDoctorInfo.outpatient_volume.status != 1">{{  extraDoctorInfo.outpatient_volume.status | statusStringFilter }}</van-tag>
              </div>  
            </div>
          </template>
          <template #input>
            <div v-if="extraDoctorInfo.outpatient_volume.content">{{ extraDoctorInfo.outpatient_volume.content }}人次</div>
          </template>
        </van-field>
        <van-dialog v-model="outpatientVolumeDialog" title="门诊量" show-cancel-button confirm-button-text="提交审核" confirm-button-color="#f39800" @confirm="allDialogConfirm(1)">
          <div style="display: flex; padding: 40px;flex-direction: row;align-items: center;justify-content: center;">
            <div>
              <van-field style="width: 100px;" input-align="center"  v-model="outpatientVolumeValue" />
              <div style="width: 100%;height: 2px;background-color: #f39800;"></div>
            </div>
            <div>人次</div>
          </div>
        </van-dialog>
        <van-field v-if="!extraDoctorInfo.visit_time.content" label-width="200" name="outpatient_volume" input-align="right" placeholder="请输入坐诊时间" @click="openAllDialog(2)">
          <template #label>
            <div style="display: flex;">
              <div style="font-size: 16px;">坐诊时间</div>
            </div>
          </template>
        </van-field>
        <van-cell v-else :label="extraDoctorInfo.visit_time.content" label-class="cell-label" @click="openAllDialog(2)">
          <template #title>
            <div style="display: flex;">
              <div style="font-size: 16px;">坐诊时间</div>
              <div>
                <van-tag round style="margin-left: 6px;" :color=" extraDoctorInfo.visit_time.status | statusColorFilter " plain v-if="extraDoctorInfo.visit_time.status > -1 && extraDoctorInfo.visit_time.status != 1">{{  extraDoctorInfo.visit_time.status | statusStringFilter }}</van-tag>
              </div>
              </div>
          </template>
        </van-cell>

        <div style="margin-top: 10px;"></div>
        <van-cell v-if="!extraDoctorInfo.hobby.content" value="待完善" value-class="value-label" @click="openAllDialog(3)">
          <template #title>
            <div style="display: flex;">
              <div style="font-size: 16px;">爱好</div>
            </div>
          </template>
        </van-cell>
        <van-cell v-else :label="extraDoctorInfo.hobby.content" label-class="cell-label" @click="openAllDialog(3)">
          <template #title>
            <div style="display: flex;">
              <div style="font-size: 16px;">爱好</div>
              <div>
                <van-tag round style="margin-left: 6px;" :color=" extraDoctorInfo.hobby.status | statusColorFilter " plain v-if="extraDoctorInfo.hobby.status > -1 && extraDoctorInfo.hobby.status != 1">{{  extraDoctorInfo.hobby.status | statusStringFilter }}</van-tag>
              </div>
              </div>
          </template>
        </van-cell>

        <van-cell v-if="!extraDoctorInfo.be_good_at.content" value="待完善" value-class="value-label" @click="openAllDialog(4)">
          <template #title>
            <div style="display: flex;">
              <div style="font-size: 16px;">擅长</div>
            </div>
          </template>
        </van-cell>
        <van-cell v-else :label="extraDoctorInfo.be_good_at.content" label-class="cell-label" @click="openAllDialog(4)">
          <template #title>
            <div style="display: flex;">
              <div style="font-size: 16px;">擅长</div>
              <div>
                <van-tag round style="margin-left: 6px;" :color=" extraDoctorInfo.be_good_at.status | statusColorFilter " plain v-if="extraDoctorInfo.be_good_at.status > -1 && extraDoctorInfo.be_good_at.status != 1">{{  extraDoctorInfo.be_good_at.status | statusStringFilter }}</van-tag>
              </div>
              </div>
          </template>
        </van-cell>

        <div style="margin-top: 10px;"></div>
        <van-cell v-if="!extraDoctorInfo.label.content" value="待完善" value-class="value-label" @click="openAllDialog(5)">
          <template #title>
            <div style="display: flex;">
              <div style="font-size: 16px;">标签</div>
            </div>
          </template>
        </van-cell>
        <van-cell v-else :label="extraDoctorInfo.label.content" label-class="cell-label" @click="openAllDialog(5)">
          <template #title>
            <div style="display: flex;">
              <div style="font-size: 16px;">标签</div>
              <div>
                <van-tag round style="margin-left: 6px;" :color=" extraDoctorInfo.label.status | statusColorFilter " plain v-if="extraDoctorInfo.label.status > -1 && extraDoctorInfo.label.status != 1">{{  extraDoctorInfo.label.status | statusStringFilter }}</van-tag>
              </div>
              </div>
          </template>
        </van-cell>

        <van-dialog v-model="commonDialog" :title="currentType | dialogTitleStatusFilter" show-cancel-button confirm-button-text="提交审核" confirm-button-color="#f39800" :beforeClose="beforeClose" @confirm="allDialogConfirm(currentType)">
          <div style="display: flex; padding: 10px;flex-direction: row;">
            <van-field style="width: 100%; background-color: #F7F7F7;" show-word-limit maxlength="250" v-model="commonDialogValue" autosize type="textarea" required :placeholder="currentType | dialogPlaceHolderStatusFilter"/>
          </div>
        </van-dialog>
      </div>
      
      
    </div>
    <van-popup v-if="doctor" v-model="showDoctorBirthdayDatePicker" position="center" style="width: 80%">
      <van-datetime-picker
      :title="doctor.name + '医生的生日'"
        v-model="currentDoctorBirthdayDate"
        type="date"
        :formatter="formatter"
        :min-date="doctorBirthdayMinDate"
        :max-date="maxDate"
        @confirm="doctorBirthdaydateOnConfirm"
        @cancel="showDoctorBirthdayDatePicker = false"
      />
    </van-popup>
  </div>
</template>

<script>
import { doctorInteractor } from '@/core'
import AvatarButton from '@/components/AvatarButton'
import { Toast, Dialog } from 'vant'
import { readFromLocalStorage } from '@/core/services/cache'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn' // ES 2015 

dayjs.locale('zh-cn')
export default {
  name: 'DoctorEditInfo',
  props: {},
  components: {
    AvatarButton,
  },
  data() {
    return {
      currentType: undefined,
      commonDialog: false,
      commonDialogValue: undefined,
      outpatientVolumeValue: undefined,
      outpatientVolumeDialog: false,
      extraDoctorInfo: undefined,
      doctor: undefined,
      doctor_extend: undefined,
      pInfo: undefined,
      query: {
        page: 0,
        size: 8,
        doctor_id: undefined,
      },
      pharmacy_type: 0,
      showPopup: false,
      currentDoctorBirthdayDate: new Date(1980, 0, 1),
      doctorBirthdayMinDate: new Date(1950, 0, 1),
      showDoctorBirthdayDatePicker: false,
      minDate: new Date(2019, 0, 1),
      maxDate: new Date(),
      showPopupBeginDatePicker: false,
      showPopupEndDatePicker: false,
      referee_promoter_name: undefined
    }
  },
  filters: {
    dialogTitleStatusFilter(status) {
      const statusMap = {
        2:"坐诊时间",
        3:"爱好",
        4:"擅长",
        5:"标签"
      }
      return statusMap[status]
    },
    dialogPlaceHolderStatusFilter(status) {
      const statusMap = {
        2:"请输入医生坐诊时间，例如：每周二、三、五上午8点至12点，下午3点至6点等",
        3:"请输入医生兴趣爱好",
        4:"请输入医生擅长",
        5:"请输入医生标签"
      }
      return statusMap[status]
    },
    statusColorFilter(status) {
      const statusMap = {
        0: '#F39800',
        2: '#EF3838',
      }
      return statusMap[status]
    },
    statusStringFilter(status) {
      const statusMap = {
        0: '审核中',
        2: '审核拒绝，请重新提交',
      }
      return statusMap[status]
    },
  },
  created() {
    let pInfo = readFromLocalStorage('info')
    console.log(pInfo)
    if (pInfo) {
      this.pInfo = pInfo
    }
    this.query.doctor_id = this.$route.query.doctor_id
    this.getDoctorInfo()
    this.fetchGetExtraDoctorInfo()
  },
  mounted() {
  },
  methods: {
    showDoctorBirthdayAction() {
      if (this.doctor.birthday) {
        
      } else {
        this.showDoctorBirthdayDatePicker = true;
      }
      
    },
    openAllDialog(type) {
      switch (type) {
        case 1:
          {
            if (this.extraDoctorInfo.outpatient_volume.status == 0) {
              Toast('审核中')
              break;
            }
            this.outpatientVolumeValue = this.extraDoctorInfo.outpatient_volume.content
            this.outpatientVolumeDialog = true
          }
          break;
        case 2:
          {
            if (this.extraDoctorInfo.visit_time.status == 0) {
              Toast('审核中')
              break;
            }
            this.currentType = type;
            this.commonDialog = true;
            this.commonDialogValue = this.extraDoctorInfo.visit_time.content
          }
          break;
        case 3:
          {
            if (this.extraDoctorInfo.hobby.status == 0) {
              Toast('审核中')
              break;
            }
            this.currentType = type;
            this.commonDialog = true;
            this.commonDialogValue = this.extraDoctorInfo.hobby.content
          }
          break;
        case 4:
          {
            if (this.extraDoctorInfo.be_good_at.status == 0) {
              Toast('审核中')
              break;
            }
            this.currentType = type;
            this.commonDialog = true;
            this.commonDialogValue = this.extraDoctorInfo.be_good_at.content
          }
          break;
        case 5:
          {
            if (this.extraDoctorInfo.label.status == 0) {
              Toast('审核中')
              break;
            }
            this.currentType = type;
            this.commonDialog = true;
            this.commonDialogValue = this.extraDoctorInfo.label.content
          }
          break;
        default:
          break;
      }
    },
    fetchGetExtraDoctorInfo() {
      doctorInteractor.fetchGetExtraDoctorInfo({doctor_id: this.query.doctor_id}).then(data => {
          this.extraDoctorInfo = data;
      })
    },
    beforeClose(action, done) {
      console.log('beforeClose')
      if (action === 'confirm') {
        if (this.outpatientVolumeValue || this.commonDialogValue) {
          done()
        } else {
          Toast('输入不能为空')
          // done()
          done(0)
        }
      } else {
        done();
      }
    },
    allDialogConfirm(type) {
      console.log('allDialogConfirm')
      let dict = { type: type, doctor_id: this.query.doctor_id, content: undefined };
      switch (type) {
        case 1:
          dict.content = this.outpatientVolumeValue;
          break;
        case 2: case 3: case 4: case 5:
          dict.content = this.commonDialogValue;
          break;
      
        default:
          break;
      }
      if (dict.content) {
        this.outpatientVolumeDialog = false
        this.fetchEditExtraDoctorInfo(dict)
      } else {
        // Toast('输入不能为空')
      }
      
    },
    fetchEditExtraDoctorInfo(dict) {
      doctorInteractor.fetchEditExtraDoctorInfo(dict).then(data => {
          this.fetchGetExtraDoctorInfo()
      }).catch( (err) => {
        // this.fetchGetExtraDoctorInfo()
      })
    },
    formatter(type, val) {
      if (type === 'year') {
        return `${val}年`;
      } else if (type === 'month') {
        return `${val}月`
      } else {
        return `${val}日`
      }
      return val;
    },
    doctorBirthdaydateOnConfirm(v) {
      this.showDoctorBirthdayDatePicker = false;
      this.fetchEditDoctorBirthday(v)
    },
    async fetchEditDoctorBirthday(d) {
      let ds = dayjs(d).format('YYYY-MM-DD')
      try {
        await doctorInteractor.fetchEditDoctorBirthday({birthday: ds, doctor_id: this.query.doctor_id}).then(data => {
          this.getDoctorInfo()
        })
      } catch (error) {
        console.log(error)
      }
    },
    async getDoctorInfo(query) {
      try {
        let q = Object.assign({}, this.query, query)
        const data = await doctorInteractor.fetchDoctorInfo(q).then(data => {
  
          this.doctor = data.doctor_info
          this.doctor_extend = data.doctor_extend
          this.referee_promoter_name = data.referee_promoter_name

        })
        // this.listTotal = total
      } catch (error) {
        console.log(error)
      }
    },

  }
}
</script>

<style lang="scss" scoped>

.cell-label {
  font-size: 16px;
  font-weight: normal;
  line-height: 22px;
  letter-spacing: 0px;

  color: #373637;
}
.value-label{
  font-size: 16px;
  font-weight: normal;
  line-height: 20px;
  text-align: right;
  letter-spacing: 0px;

  color: #2781D7;
}
</style>
