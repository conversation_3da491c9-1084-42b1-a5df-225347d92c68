<template>
  <div>
    <div class="mid-card" style="height: 262px;  margin-top: 20px">
      <div style="display: flex;justify-content: space-between;">
        <CommonHeader style="padding: 15px 10px" title="订单业绩"/>
        <div>
          <div style="display: flex;align-items: center;margin-right: 10px;">
            <div style="font-size: 10px; color: gray" v-if="counts">{{counts.begin_time.substring(0,10) + ' 至 ' + counts.end_time.substring(0,10)}}</div>
            <van-dropdown-menu>
              <van-dropdown-item v-model="timePicker" :options="timeOption" />
            </van-dropdown-menu>
          </div>
        </div>
      </div>
      <van-row type="flex" justify="space-around" v-if="counts">
        <van-col span="11"><DoctorStatisticsTitle title="订单笔数" :count="counts.orders.order_number"/></van-col>
        <van-col span="11"><DoctorStatisticsTitle title="订单金额" :count="counts.orders.order_total_amount"/></van-col>
      </van-row>
      <div v-if="info && info.promoter_type == 1">
        <van-cell style="margin-top:10px" title="药费金额" value-class="cell-value" :value="'¥' + counts.orders.base_medicine_charge" v-if="counts"/>
        <!-- <van-cell title="纯销金额" value-class="cell-value" :value="'¥' + counts.orders.order_amount" v-if="counts"/>
        <van-cell title="奖金预估" value-class="cell-value" :value="'¥' + counts.orders.bonus_predict" v-if="counts"/> -->
      </div>
      
    </div>
    <div class="mid-card" style="height: 132px; margin-top: 20px">
      <CommonHeader style="padding: 15px 10px" title="医生数据看板"/>
      <van-row type="flex" justify="space-around" v-if="counts">
        <van-col span="5"><DoctorStatisticsTitle title="邀请医生数" :count="counts.doctors.doctors"/></van-col>
        <van-col span="5"><DoctorStatisticsTitle title="已认证医生" :count="counts.doctors.credentials"/></van-col>
        <van-col span="5"><DoctorStatisticsTitle title="未认证医生" :count="counts.doctors.not_credentials"/></van-col>
        <van-col span="6"><DoctorStatisticsTitle title="认证拒绝医生" :count="counts.doctors.refuse_credentials"/></van-col>
      </van-row>
    </div>
    <div  class="mid-card" style="height: 105px; margin-top: 20px; margin-bottom:0px">
      <CommonHeader style="padding: 15px 10px" :title="('代理商数：' + list.length + '个')"/>
      <van-cell v-for="(item, index) in list" :key="index"  :title="item.name"  is-link  @click="clickCell(item)"/>
    </div>
  </div>
</template>

<script>

import { pharmacyInteractor } from '@/core'
import CommonHeader from '@/components/CommonHeader'
import DoctorStatisticsTitle from '@/components/DoctorStatisticsTitle'
import { readFromLocalStorage } from '@/core/services/cache'
import { Toast, Dialog } from 'vant'
import dayjs from 'dayjs'
import quarterOfYear from 'dayjs/plugin/quarterOfYear' // ES 2015
import 'dayjs/locale/zh-cn' // ES 2015 

export default {
  name: 'PharmacyList',
  components: {
    CommonHeader, DoctorStatisticsTitle
  },
  props: {},
  data() {
    return {
      info: undefined,
      counts: undefined,
      list: [],
      query: {
        begin_time: undefined,
        end_time: undefined 
      },
      timePicker: 0,
      timeOption: [
        { text: '本月', value: 0 },
        { text: '本日', value: 1 },
        { text: '本周', value: 2 },
        { text: '本季度', value: 3 },
        { text: '上季度', value: 4 },
        { text: '本年度', value: 5 },
      ],
      loading: false
    }
  },
  watch: {
      timePicker: function (val, oldVal) {
      console.log('new a: %s, old a: %s', val, oldVal)
      let b,e
      let now = new Date
      switch (val) {
          case 0:
              b = new Date(now.getFullYear(), now.getMonth(), 1)
              e = now
              break;
          case 1:
              b = now
              e = now
              break;
          case 2:
              let w = dayjs(now).day(1)
              b = w.$d
              e = now
              console.log(w)
              break;
          case 3:
              let q = dayjs(now).quarter()
              let d = dayjs(now.getFullYear().toString()).quarter(q)
              b = d.$d
              e = now
              break;
          case 4:
              let qq = dayjs(now).quarter()
              let dd = dayjs(now.getFullYear().toString()).quarter(qq - 1)
              let ww = dayjs(now.getFullYear().toString()).quarter(qq - 1).endOf('quarter')
              b = dd.$d
              e = ww.$d
              console.log(dayjs(e).format('YYYY-MM-DD'))
              break;
          case 5:
              b = new Date(now.getFullYear(),0,1,0,0,0)
              e = now
              break;
      }
      this.query.begin_time = dayjs(b).format('YYYY-MM-DD') 
      this.query.end_time = dayjs(e).format('YYYY-MM-DD') 
      this.getPharmacyList(this.query)
      this.getPharmacyInfo(this.query)
    },
  },
  created() {},
  mounted() {
    let info = readFromLocalStorage('info')
    console.log(info)
    if (info) {
      this.info = info
    } 
    this.getPharmacyList(this.query)
    this.getPharmacyInfo(this.query)
  },
  methods: {
    clickCell(item) {
      this.$router.push({path:'/pharmacyDetail',query: item})
    },
    async getPharmacyInfo(query) {
      try {
        let q = Object.assign({}, query, query)
        if (q.begin_time) {
          q.begin_time = dayjs(query.begin_time).format('YYYY-MM-DD') 
        }
        if (q.end_time) {
          q.end_time = dayjs(query.end_time).format('YYYY-MM-DD') 
        }

        await pharmacyInteractor.fetchPharmacyInfo(q).then(data => {
          this.counts = data
        })
        
      } catch (error) {
        console.log(error)
      }
    },
    async getPharmacyList(query) {
      try {
        let q = Object.assign({}, query, query)
        if (q.begin_time) {
          q.begin_time = dayjs(query.begin_time).format('YYYY-MM-DD') 
        }
        if (q.end_time) {
          q.end_time = dayjs(query.end_time).format('YYYY-MM-DD') 
        }
        q.size = 100
        await pharmacyInteractor.fetchPharmacyList(q).then(data => {
          if (this.query.page === 1) {
            this.list = data.pharmacys
          } else {
            this.list = [...this.list, ...data.pharmacys]
          }
          if (data.total === this.list.length) {
            this.finished = true
          } else {
            this.finished = false
          }
          console.log(this.list)
          this.loading = false
        })
        
      } catch (error) {
        console.log(error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .van-dropdown-item--down {
  margin: 0px 6% !important;
}
.mid-card {
  margin: 0px -6% 0px 6%;
  width: 88%;
  background-color: white;
  border-radius: 5px;
}
.van-cell {
  background-color: #fcfcfc;
  margin: 0px 2% 0px 2%;
  border-radius: 5;
  width: 96%;
}
.cell-value {
  color: black;
  font-weight: bold;
}
</style>
