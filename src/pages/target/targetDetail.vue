<template>
  <div>
    <van-dropdown-menu active-color="#ff9000">
        <van-dropdown-item v-model="query.target_type" :options="target_type_option" @change="typeChange"/>
        <van-dropdown-item title="时间范围" ref="item" @open="showDatePopup = true">
        </van-dropdown-item>
    </van-dropdown-menu>
    <van-popup
      v-model="showDatePopup"
      position="center"
      style="width: 80%"
    >
      <van-picker
        title="选择年"
        show-toolbar
        :columns="yearColumns"
        @confirm="confirmDatePicker"
        @cancel="showDatePopup = false"
      />
      <!-- <van-datetime-picker
        v-model="pickerDate"
        :columns-order="['year']"
        type="year-month"
        title="选择年"
        :formatter="dateFormatter"
        @confirm="confirmDatePicker"
        @cancel="showDatePopup = false"
      /> -->
    </van-popup>
    <!-- <van-calendar v-model="calendarShow" ref="calendar" type="range" :min-date="minDate" color="#ff9000" title="时间范围" @confirm="calendarOnConfirm" >
      <template #footer>
        <div v-if="query"></div>
        <div style="display: flex;flex-direction: row;justify-content: space-around;padding: 10px 0">
          <van-button round color="#ff9000" plain style="width: 150px" @click="calendarReset">重置</van-button>
          <van-button round color="#ff9000" style="width: 150px" @click="calendarOnConfirm($refs.calendar.currentDate)">确认</van-button>
        </div>
      </template>
    </van-calendar> -->
    <div style="display: flex;justify-content: space-around;padding: 10px;background-color: white;" v-if="data">
      <div class="year-flex">
        <div v-if="query.target_type == 1" class="year-title">年度目标金额</div>
        <div v-if="query.target_type == 2" class="year-title">年度注册医生目标</div>
        <div v-if="query.target_type == 3" class="year-title">年度认证医生目标</div>
        <div>{{query.target_type == 1 ? "¥" :""}}{{data.year_target_amount}}</div>
      </div>
      <div class="year-flex">
        <div v-if="query.target_type == 1" class="year-title">年度完成金额</div>
        <div v-if="query.target_type == 2" class="year-title">年度完成注册医生</div>
        <div v-if="query.target_type == 3" class="year-title">年度完成认证医生</div>
        <div>{{query.target_type == 1 ? "¥" :""}}{{data.year_complete_amount}}</div>
      </div>
      <div class="year-flex">
        <div class="year-title">完成率</div>
        <div>{{data.year_complete_percent}}%</div>
      </div>
    </div>
    <van-pull-refresh v-model="loading" @refresh="onRefresh">
        <van-list
          v-model="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
          offset="150"
        >
        <!-- <div class="cell" v-for="(item, index) in list" :key="index">
          <div style="width:100%">
            <div>s</div>
          </div>
        </div> -->
        <v-table
          :columns="columns"
          :table-data="list"
          :row-height="24"
          is-horizontal-resize
          title-bg-color="#f3f3f3"
          style="width:100%"
          :column-cell-class-name="columnCellClass"
          :show-vertical-border="false">
        </v-table>
        </van-list>
      </van-pull-refresh>
  </div>
</template>

<script>
import { indexInteractor } from '@/core'
import dayjs from 'dayjs'
import quarterOfYear from 'dayjs/plugin/quarterOfYear' // ES 2015
import 'dayjs/locale/zh-cn' // ES 2015 

dayjs.locale('zh-cn')
dayjs.extend(quarterOfYear)
export default {
  name: 'targetDetail',
  props: {},
  data() {
    return {
      data: undefined,
      loading: false,
      finished: false,
      refreshing: false,
      query: {
        target_type: 1,
        type: 3,
        begin_time: undefined,
        end_time: undefined,
        year: undefined,
        page: 0,
        size: 12
      },
      list: [],
      target_type_option: [
        { text: '处方金额', value: 1 },
        { text: '注册医生数', value: 2 },
        { text: '认证医生数', value: 3 }
      ],
      showDatePopup: false,
      pickerDate: undefined,
      calendarShow: false,
      minDate: new Date(2019, 0, 1),
      yearColumns: [
        '2020',
        '2021',
        '2022',
        '2023',
        '2024',
        '2025',
        '2026',
        '2027',
        '2028',
      ],
      columns: [
        {field: 'promoter_name', title:'姓名', width: 80, titleAlign: 'center', columnAlign:'center', titleCellClassName:'title-cell-class-name-t'},
        {field: 'year', formatter: this.timeFormatter, title: '时间',  width: 60, titleAlign: 'center', columnAlign:'center', titleCellClassName:'title-cell-class-name-t'},
        // {field: 'month', title: '月',  width: 30, titleAlign: 'center', columnAlign:'center', titleCellClassName:'title-cell-class-name-t'},
        {field: 'target_amount', title: '目标数', width: 80, titleAlign: 'center', columnAlign:'center', titleCellClassName:'title-cell-class-name-t'},
        {field: 'complete_amount', title: '完成数', width: 80, titleAlign: 'center', columnAlign:'center', titleCellClassName:'title-cell-class-name-t'},
        {field: 'complete_percent', formatter: this.percentFormatter, title: '完成度', width: 70, titleAlign: 'center', columnAlign:'center', titleCellClassName:'title-cell-class-name-t'}
      ]
    }
  },
  created() {
    this.query.type = this.$route.query.type
  },
  mounted() {
  },
  methods: {
    confirmDatePicker(value) {
      this.showDatePopup = false
      this.query.year = value
      this.fetchTargetList(this.query)
    },
    timeFormatter(rowData,rowIndex,pagingIndex,field) {
      return rowData.year + '-' + rowData.month;
    },
    percentFormatter(rowData,rowIndex,pagingIndex,field) {
      return rowData.complete_percent + '%';
    },
    columnCellClass(rowIndex,columnName,rowData){
      return 'column-cell-class-name-t';
    },
    typeChange() {
      this.query.page = 1
      this.list = []
      this.finished = false
      this.fetchTargetList(this.query)
    },
    calendarReset() {
      this.calendarShow = false;
      this.$refs.calendar.reset();
      this.query.begin_time = undefined
      this.query.end_time = undefined
      this.query.page = 1
      this.list = []
    },
    calendarOnConfirm(date) {
      console.log(date)
      this.query.begin_time =  dayjs(date[0]).format('YYYY-MM-DD')
      this.query.end_time =  dayjs(date[1]).format('YYYY-MM-DD')
      this.calendarShow = false;
      this.fetchTargetList(this.query)
    },
    async fetchTargetList(query){
      try {
        let q = Object.assign({}, query, query)
        await indexInteractor.fetchTargetList(q).then(data => {
          if (this.query.page === 1) {
            this.list = data.list
          } else {
            this.list = [...this.list, ...data.list]
          }
          if (data.total <= this.list.length) {
            this.finished = true
          }
          console.log(this.list)
          this.data = data
          this.loading = false
        })
      } catch (error) {
        console.log(error)
      }
    },
    onLoad() {
      // 异步更新数据
      // setTimeout 仅做示例，真实场景中一般为 ajax 请求
      this.loading = true
      this.query.page = this.query.page + 1
      console.log('onLoad')
      this.fetchTargetList(this.query)
    },
    onRefresh() {
      // 清空列表数据
      this.finished = false
      console.log('onRefresh')
      console.log(this.query)
      // 重新加载数据
      // 将 loading 设置为 true，表示处于加载状态
      this.loading = true
      this.query.page = 1
      this.list = []
      this.fetchTargetList(this.query)
    }
  }
}
</script>

<style lang="scss" scoped>
.cell {
  padding: 8px 0 0 0 ;
}
::v-deep .van-cell-group--inset {
  margin: 0 8px;
}

::v-deep .column-cell-class-name-t{
  font-size: 10px;
}
::v-deep .table-title {
  font-size: 12px;
}
.year-flex {
  text-align: center;
  font-size: 14px;
}
.year-title {
  color: gray;
  padding-bottom: 6px;
}
</style>
