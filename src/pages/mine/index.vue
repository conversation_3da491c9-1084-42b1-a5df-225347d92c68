<template>
  <div>
    <div style="position:relative; z-index:-2;">
        <!-- <img src="~@/assets/home/<USER>" style="position:absolute; z-index:0; width:100%"> -->
      <div style="position:absolute; z-index:0;width:100%; height:70px; background-color: #ff9000"></div>
    </div>
    <div style="position:relative; z-index:1; display:flex" v-if="info">
      <div class="header-view">
        <van-image
          round
          class="bottom-img"
          width="60"
          :src="info.avatar"
        />
        <div class="info-view" v-if="info">
          <div style="display: flex;justify-content: space-between;">
            <div class="info-title">
              <div style="color: black;">{{info.name}}</div>
              <van-button style="margin-left:10px" color="#f6d21c" round size="mini">{{info.role_id != 8 ? info.level : info.role}}</van-button>
            </div>
            <van-button style="margin-right:10px;font-size: 14px;" color="#f39000" round size="mini" @click="logoutAction" >账号退出</van-button>
          </div>
          
          <div style="padding-top: 8px;font-size: 12px;display: flex;">
            <div v-if="info.parent_name" style="color: black; margin-right: 10px;">我的上级:{{info.parent_name}}</div>
            <div style="color: black;">所属渠道:{{info.pharmacy}}</div>
          </div>
        </div>
      </div>
    </div>

    <div v-if="info.level_id > 1" style="margin: 0px 0px 10px 0;background-color: white;">
      <div style="display: flex;align-items: center;">
          <CommonHeader style="padding: 10px 0px 0 0" title='医生关怀'/>
      </div>
      <div style="padding: 26px 10px 20px 10px;display: flex;justify-content: space-around;" v-if="doctorCareNotice">
        <div class="bage-item" @click="itemAction(1)">
          <van-badge :dot="doctorCareNotice.loss_warning == 1" >
            <van-image
              width="30px"
              height="30px"
              :src="require('@/assets/mine/h_0.png')"
            />
          </van-badge>
          <div class="badge-text">流失预警</div>
        </div>
        <div class="bage-item" @click="itemAction(2)">
          <van-badge :dot="doctorCareNotice.maintenance_reminder == 1">
            <van-image
              width="30px"
              height="30px"
              :src="require('@/assets/mine/h_1.png')"
            />
          </van-badge>
          <div class="badge-text">维护提醒</div>
        </div>
        <div class="bage-item" @click="itemAction(3)">
          <van-badge :content="doctorCareNotice.birthday_reminder > 0 ? doctorCareNotice.birthday_reminder : undefined">
            <van-image
              width="30px"
              height="30px"
              :src="require('@/assets/mine/h_2.png')"
            />
          </van-badge>
          <div class="badge-text">生日提醒</div>
        </div>
      </div>
    </div>
    
    <van-cell title="喜郎中APP版本更新记录" is-link value-class="version-value" to="/versionList">
      <template #default>
        <van-badge dot>
            <span>V{{version}}</span>
          </van-badge>
      </template>
    </van-cell>
    <!-- <van-cell title="个人积分" is-link  to="/points">
    </van-cell> -->
    <van-cell title="我的收益" is-link value-class="version-value" to="/myEarnings">
    </van-cell>
    <van-cell title="积分商城" v-if="info && info.owner_id == 4" is-link  to="/mallList">
    </van-cell>
    <!-- <van-cell style="margin-top: 10px" title="账号退出" is-link @click="logoutAction" /> `-->
    <div class="card-list-view"  v-if="info && info.owner_id == 4" @click="pointsAction">
      <div style="display: flex;align-items: center;width: 100%;justify-content: space-between;padding: 20px 0px;">
        <div style="display: flex;align-items: center;">
          <!-- <van-image
              width="40px"
              height="40px"
              :src="require('@/assets/mine/p_h_1.png')"
            /> -->
            <CommonHeader style="padding: 0px 0px 0 0" title='积分任务'/>
        </div>
        <div style="color: #A4A4A4;font-size: 14px; padding-right: 16px;display: flex;align-items: center;"><div style="color: #f39000;font-size: 14px;">可用积分{{ info.point }}</div><van-icon  name="arrow" /></div>
      </div>
    </div>
    <div v-if="info && info.owner_id == 4" v-for="(item,index) in list" :key="index" style="width:100%">
      <van-cell-group>
        <van-cell :title="item.title" :label="item.description" :center="true">
          <template #default >
            <div style="">
              <van-tag color="#FFF3E4" text-color="#F39800" size="large">+{{ item.point }}</van-tag>
            </div>
          </template>
        </van-cell>
      </van-cell-group>
    </div>
    <div style="height: 60px;"></div>
    <router-view />
    <van-tabbar route active-color="#F7941E">
      <van-tabbar-item replace to="/home" icon="home-o">
        首页
      </van-tabbar-item>
      <van-tabbar-item v-if="info && info.level_id != 1" replace to="/doctors" icon="manager-o">
        医生
      </van-tabbar-item>
      <van-tabbar-item v-if="info && info.level_id != 4" replace to="/team" icon="friends-o">
        我的团队
      </van-tabbar-item>
      <van-tabbar-item replace to="/mine" icon="user-circle-o" dot >
        我的
      </van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script>

import { indexInteractor, mallInteractor } from '@/core'
import { saveToLocalStorage, readFromLocalStorage, removeToken } from '@/core/services/cache'
import { Toast, Dialog } from 'vant'
import CommonHeader from '@/components/CommonHeader'
export default {
  name: 'Mine',
  props: {},
  components: {
    CommonHeader
  },
  data() {
    return {
      info: undefined,
      doctorCareNotice: undefined,
      version: '',
      list:[]
    }
  },
  created() {
    let info = readFromLocalStorage('info')
    this.info = info
    this.$bus.$on("refreshUserInfo", (info) => {
      console.log(info);
      this.info = info
      // this.messagesCount = count
    });
  },
  mounted() {
    console.group('mounted 挂载结束状态*********************》');
    this.fetchVersionList()
    this.fetchDoctorCareNotice()
    this.fetchList()
    
  },
  watch: {
    '$route' (to, from) {
      console.group('route 更新状态*********************》');
      console.log(to);
      console.groupEnd();
      if (to.name == 'Mine') {
        this.fetchVersionList()
        this.fetchDoctorCareNotice()
        this.fetchList()
      }
      
    }
},
  methods: {
    async fetchVersionList() {
      try {
        const info = await indexInteractor.getVersionList()
        const data = Object.assign({}, info)
        this.version = data.list[0].version_num
      } catch (error) {
        console.log(error)
      }
    },
    async fetchDoctorCareNotice() {
      try {
        const info = await indexInteractor.fetchDoctorCareNotice()
        const data = Object.assign({}, info)
        console.log(data)
        this.doctorCareNotice = data
        // this.version = data.list[0].version_num
      } catch (error) {
        console.log(error)
      }
    },
    fetchList () {
      mallInteractor.fetchPointIndex().then(data => {
        this.list = data.list;
      })
    },
    pointsAction() {
      this.$router.push({path:'/points'})
    },
    itemAction(type) {
      this.$router.push({path:'/doctorCareList',query: { type : type}})
    },
    logoutAction() {
      Dialog.confirm({
        title: '温馨提示',
        message: '是否退出登录',
      })
        .then(() => {
          removeToken()
          this.$router.push('/login')
        })
        .catch(() => {
          // on cancel
        });
    },
  }
}
</script>

<style lang="scss" scoped>
  .bage-item {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .badge-text {
    padding-top: 4px;
    text-align: center;
    font-size: 14px;
  }
.bottom-img {
  margin-left: 10px;
}
.info-view {
  width: 100%;
  margin-left: 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.info-title {
  display: flex;
  align-items: center;
}
.header-view {
  width: 100%;
  height: 80px;
  margin: 30px 10px 10px 10px;
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 10px;
}
.version-value {
  overflow: revert;
}

.card-list-view {
  margin: 10px 0 0 0;
  // padding: 0 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: white;
}

::v-deep .van-cell__title, .van-cell__value{
  flex-basis: auto !important;
}
</style>
