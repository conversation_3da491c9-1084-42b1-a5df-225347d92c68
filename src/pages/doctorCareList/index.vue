<template>
  <div>
    <van-pull-refresh v-model="loading" @refresh="onRefresh" class="list-container">
      <van-list
        v-model="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
        offset="150"
      >
      <div v-for="(item,index) in list" class="cell">
        <div style="display: flex" @click="itemOnAction(item)">
          <AvatarButton style="margin-top: 10px;" :url="item.url ? item.url : ''" :statusString="item.credential_status" :status="99"/>
          <div style="width:100%">
            <van-cell :title="((item.doctor_name ? item.doctor_name : '') + '  ' + item.mobile)" >
              <template v-if="query.type == 3" #right-icon>
                <div style="color: #f39000">
                  <van-icon name="point-gift-o" size="18" />
                  {{ item.birthday }}
                </div>
              </template>
            </van-cell>
            <van-cell v-if="query.type == 1" :title="'最后处方时间:' + item.end_prescription_time" />
            <van-cell v-if="query.type == 2" :title="'最后登录时间:' + item.end_login_time" />
            <van-cell v-if="query.type == 3" :title="item.daystobd > 0 ? `还有${item.daystobd}天就是老师的生日了，记得送上祝福哦` : '今天就是老师的生日了，记得送上祝福哦'" />
          </div>
        </div>
      </div>
      </van-list>
    </van-pull-refresh>
  </div>
</template>

<script>
import { indexInteractor } from '@/core'
import AvatarButton from '@/components/AvatarButton'
export default {
  name: 'DoctorCareList',
  props: {},
  components: {
    AvatarButton
  },
  data() {
    return {
      list: [],
      loading: false,
      finished: false,
      refreshing: false,
      total: 0,
      query: {
        type: 1,
        page: 0,
        size: 10
      },
    }
  },
  created() {
    this.query.type = this.$route.query.type
  },
  mounted() {},
  methods: {
    async fetchDoctorCareList(query) {
      try {
        let q = Object.assign( query, query)
        console.log(q)
        await indexInteractor.fetchDoctorCareList(q).then(data => {
          console.log(data)
          if (this.query.page === 1) {
            this.list = data.list
          } else {
            this.list = [...this.list, ...data.list]
          }
          if (data.total === this.list.length) {
            this.finished = true
          } else {
            this.finished = false
          }
          this.loading = false
          this.total = data.total
        })
      } catch (error) {
        console.log(error)
      }
    },
    itemOnAction(item) {
      this.$router.push({path:'/doctorDetail',query: { doctor_id : item.doctor_id}})
    },
    onLoad() {
      // 异步更新数据
      // setTimeout 仅做示例，真实场景中一般为 ajax 请求
      this.loading = true
      this.query.page = this.query.page + 1
      console.log('onLoad')
      this.fetchDoctorCareList(this.query)
    },
    onRefresh() {
      // 清空列表数据
      this.finished = false
      console.log('onRefresh')
      // 重新加载数据
      // 将 loading 设置为 true，表示处于加载状态
      this.loading = true
      this.query.page = 1
      this.list = []
      this.fetchDoctorCareList(this.query)
    }
  }
}
</script>

<style lang="scss" scoped>
.cell {
  background-color: white;
  margin: 10px;
  // height: 118px;
  border-radius: 10px;
  overflow: hidden;
}
::v-deep .van-cell {
  font-size: 12px;
  padding: 10px;
}
::v-deep .van-panel__header-value {
  color: #ff9000;
}
</style>
