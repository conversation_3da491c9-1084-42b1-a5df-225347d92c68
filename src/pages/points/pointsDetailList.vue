<template>
  <div>
    <div style="position:relative; z-index:-2;">
      <div class="main-header"></div>
    </div>
    <div style="position:relative; z-index:1; display:flex; flex-direction: column;">
      <div class="header-view" v-if="info">
        <div>
          <div style="font-size: 25px;font-weight: bold;color: #F39800;">{{ info.point }}积分</div>
        </div>
        <div class="header-right">
          <van-image
              :src="require('@/assets/mine/p_d_l_0.png')"
            />
        </div>
      </div>
      <div class="header-card-view">
        <van-tabs v-model="active" style="width:100%;" color="#F39800" title-active-color="#F39800">
          <van-tab title="收支明细" title-style="font-size: 16px;font-weight: bold;">
            <van-tabs type="card" color="#F39800" style="padding: 10px 0;" v-model="type" @change="typeOnchange()">
              <van-tab title="全部"></van-tab>
              <van-tab title="获取"></van-tab>
              <van-tab title="支出"></van-tab>
            </van-tabs>
            <van-pull-refresh v-model="pointLoading" @refresh="pointOnRefresh">
              <van-list
                v-model="pointLoading"
                :finished="pointFinished"
                finished-text="没有更多了"
                offset="150"
                @load="pointOnLoad"
              >
                <div v-for="(item, index) in pointList" :key="index" class="cell" @click="clickItem(item)">
                  <van-cell-group>
                    <van-cell :title="item.remarks" :label="item.created_at" :center="true">
                      <template #default >
                        <div style="">
                          <van-tag v-if="item.type == 1" color="#FFF3E4" text-color="#F39800" size="large">+{{ item.point }}</van-tag>
                          <van-tag v-if="item.type == 2" color="#FFF0F0" text-color="#EF3838" size="large">-{{ item.point }}</van-tag>
                        </div>
                      </template>
                    </van-cell>
                  </van-cell-group>
                </div>
              </van-list>
            </van-pull-refresh>

          </van-tab>
          <van-tab title="兑换记录" title-style="font-size: 16px;font-weight: bold;">
            <van-pull-refresh v-model="loading" @refresh="onRefresh">
              <van-list
                v-model="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="150"
                @load="onLoad"
              >
                <div v-for="(item, index) in list" :key="index" class="cell">
                  <van-panel :title="'单号:'+ item.no"  :status="goodsStatus(item)">
                    <div style="display:flex; margin: 10px 0 0 10px">
                      <div>
                        <van-image width="100" height="100" v-if="item" :src="item.items[0].goods.cover_image_file.url" />
                      </div>
                      <div style="width: 100%;">
                        <van-cell class="item-cell" :title="item.items[0].goods.name"  :border=false title-class="goods-name" >
                        </van-cell>
                        <van-cell class="item-cell" :title="item.promoter_integral + '积分'" :border=false title-class="des" :value="'数量：' + item.items[0].quantity"/>
                      </div>
                    </div>
                    <template #footer>
                      <div style="display: flex;flex-direction: row-reverse;">
                        <van-button size="mini" plain color="#f39800" @click="statusButton(item)">{{goodsStatusButton(item)}}</van-button>
                      </div>
                    </template>
                  </van-panel>
                </div>
              </van-list>
            </van-pull-refresh>

          </van-tab>
        </van-tabs>
      </div>
    </div>
  </div>
</template>

<script>
import { mallInteractor, indexInteractor } from '@/core'
import { Toast, Dialog } from 'vant'
export default {
  name: 'Points',
  props: {},
  data() {
    return {
      active: 0,
      info: undefined,
      type: 0,
      pointList: [],
      pointLoading: false,
      pointFinished: false,
      pointRefreshing: false,
      pointQuery: {
        type: undefined,
        page: 0,
        size: 5,
      },
      list: [],
      loading: false,
      finished: false,
      refreshing: false,
      query: {
        page: 0,
        size: 5,
      }
    }
  },
  created() {},
  mounted () {
    this.fetchInfo()
  },
  methods: {
    async fetchInfo() {
      try {
        const info = await indexInteractor.getPromoterInfo()
        const data = Object.assign({}, info)
        this.info = data
        this.infoPoint = this.info.point
        saveToLocalStorage('info', data)
      } catch (error) {
        console.log(error)
      }
    },
    itemAction() {
      this.$router.push({path:'/mallList'})
    },
    statusButton(item) {
      console.log(item)
      if (item.status != 2) {
        Dialog.alert({
          title: '温馨提示',
          message: '请拨打客服电话：4000918999',
        }).then(() => {
          // on close
          window.location.href = 'tel:4000918999';
        });
      } else {
        this.$router.push({ path: '/mallExpress?no=' + item.expno})
      }
      
    },
    typeOnchange() {
      if (this.type == 0) {
        this.pointQuery.type = undefined;
      } else {
        this.pointQuery.type = this.type;
      }
      this.pointOnRefresh();
    },
    pointOnLoad () {
      console.log('onLoad')
      this.pointLoading = true
      this.pointQuery.page = this.pointQuery.page + 1
      this.fetchPointLogs(this.pointQuery)
    },
    pointOnRefresh() {
      // 清空列表数据
      this.pointFinished = false
      this.pointLoading = true
      this.pointQuery.page = 1
      this.pointList = []
      this.fetchPointLogs(this.pointQuery)
    },
    fetchPointLogs (pointQuery) {
      mallInteractor.fetchPointLogs(pointQuery).then(data => {
        // this.pointList = data.list;
        if (this.pointQuery.page === 1) {
          this.pointList = data.list
        } else {
          this.pointList = [...this.pointList, ...data.list]
        }
        if (data.total <= this.pointList.length) {
          this.pointFinished = true
        }
        this.pointLoading = false
      })
    },
    goodsStatus(item) {
      if (item.status == 1) {
        return '待发货'
      } else if (item.status == 2) {
        return '待收货'
      } else if (item.status == 3) {
        return '已完成'
      } else if (item.status == 4) {
        return '已退款'
      }
    },
    goodsStatusButton(item) {
      if (item.status == 1) {
        return '提醒发货'
      } else if (item.status == 2) {
        return '查询物流'
      } else {
        return '联系客服'
      }
    },
    onLoad () {
      console.log('onLoad')
      this.loading = true
      this.query.page = this.query.page + 1
      this.fetchMallIntegralOrderList(this.query)
    },
    onRefresh() {
      // 清空列表数据
      this.finished = false
      this.loading = true
      this.query.page = 1
      this.list = []
      this.fetchMallIntegralOrderList(this.query)
    },
    //订单列表
    async fetchMallIntegralOrderList (query) {
      await mallInteractor.fetchMallIntegralOrderList(query).then(data => {
        if (this.query.page === 1) {
          this.list = data.orders
        } else {
          this.list = [...this.list, ...data.orders]
        }
        if (data.total <= this.list.length) {
          this.finished = true
        }
        this.loading = false
      })
    },
  }
}
</script>

<style lang="scss" scoped>

.main-header {
  position: absolute;
  height: 140px;
  z-index:0;
  width:100%; 
  background: #FFF6E7;
}

.header-right {
  // width: 103px;
  padding-right: 20px;
  // height: 31px;

}

.header-view {
  // width: 100%;
  // height: 80px;
  margin: 30px 0px 10px 10px;
  display: flex;
  color: white;
  flex-direction: row;
  border-radius: 10px;
  justify-content: space-between;
  align-items: center;
}

.header-card-view {
  // width: 100%;
  margin: 10px 10px 10px 10px;
  padding: 0 15px;
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 10px;
  justify-content: space-between;
}
.card-list-view {
  margin: 10px 10px 10px 10px;
  // padding: 0 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: white;
  border-radius: 10px;
}

::v-deep .van-panel__header {
  padding: 10px 0px !important;
}

::v-deep .van-panel__header-value {
  color: #F39800 !important;
}
.goods-name {
  display: inline-block;
  white-space: nowrap; 
  width: 120px; 
  overflow: hidden;
  text-overflow:ellipsis;
}

</style>
