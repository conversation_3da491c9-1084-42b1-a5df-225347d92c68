<template>
  <div>
    <div style="position:relative; z-index:-2;">
      <div class="main-header"></div>
    </div>
    <div style="position:relative; z-index:1; display:flex; flex-direction: column;">
      <div class="header-view" v-if="info">
        <div>
          <div style="font-size: 18px;font-weight: bold;">可用积分</div>
          <div style="font-size: 25px;font-weight: bold;">{{ info.point }}</div>
        </div>
        <div class="header-right" @click="jumpToPointsDetailList()">
          收支明细
          <van-icon name="arrow" />
        </div>
      </div>
      <div class="header-card-view" @click="itemAction()">
        <div style="display: flex;align-items: center;">
          <van-image
              width="40px"
              height="40px"
              :src="require('@/assets/mine/p_h_0.png')"
            />
          <div style="padding-left: 8px;">逛商城兑好物</div>
        </div>
        <van-icon name="arrow" color="#F99B17"/>
      </div>
      <div class="card-list-view">
        <div style="display: flex;align-items: center;width: 100%;justify-content: space-between;padding: 20px 10px;">
          <div style="display: flex;align-items: center;">
            <van-image
                width="40px"
                height="40px"
                :src="require('@/assets/mine/p_h_1.png')"
              />
            <div style="padding-left: 8px;">做任务赚积分</div>
          </div>
          <div style="color: #A4A4A4;font-size: 14px;">每日00:00更新数据</div>
        </div>
        <div style="width:100%"><van-divider style="margin: 0px;"/></div>
        
          <div v-for="(item,index) in list" :key="index" style="width:100%">
            <van-cell-group>
              <van-cell :title="item.title" :label="item.description" center="true">
                <template #default >
                  <div style="">
                    <van-tag color="#FFF3E4" text-color="#F39800" size="large">+{{ item.point }}</van-tag>
                  </div>
                </template>
              </van-cell>
            </van-cell-group>
          </div>
        
        
      </div>
    </div>
  </div>
</template>

<script>
import { readFromLocalStorage } from '@/core/services/cache'
import { indexInteractor, mallInteractor } from '@/core'
export default {
  name: 'Points',
  props: {},
  data() {
    return {
      info: undefined,
      infoPoint: undefined,
      list: []
    }
  },
  created() {},
  mounted () {
    this.fetchInfo()
    this.fetchList()
  },
  methods: {
    async fetchInfo() {
      try {
        const info = await indexInteractor.getPromoterInfo()
        const data = Object.assign({}, info)
        this.info = data
        this.infoPoint = this.info.point
        saveToLocalStorage('info', data)
      } catch (error) {
        console.log(error)
      }
    },
    jumpToPointsDetailList() {
      this.$router.push({path:'/pointsDetailList'})
    },
    itemAction() {
      this.$router.push({path:'/mallList'})
    },
    fetchList () {
      mallInteractor.fetchPointIndex().then(data => {
        this.list = data.list;
      })
    },
  }
}
</script>

<style lang="scss" scoped>

::v-deep .van-cell__title, .van-cell__value{
  flex-basis: auto !important;
}

.main-header {
  position: absolute;
  height: 140px;
  z-index:0;
  width:100%; 
  background: linear-gradient(0deg, #F99B17 -2%, #F86E08 99%);
}

.header-right {
  width: 103px;
  height: 31px;
  border-radius: 80px 0px 0px 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 1;
  background: linear-gradient(270deg, #FF3E2C 8%, #FF6A00 93%);
}

.header-view {
  // width: 100%;
  // height: 80px;
  margin: 30px 0px 10px 10px;
  display: flex;
  color: white;
  flex-direction: row;
  border-radius: 10px;
  justify-content: space-between;
  align-items: center;
}

.header-card-view {
  // width: 100%;
  height: 80px;
  margin: 10px 10px 10px 10px;
  padding: 0 15px;
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 10px;
  justify-content: space-between;
}
.card-list-view {
  margin: 10px 10px 10px 10px;
  // padding: 0 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: white;
  border-radius: 10px;
}

</style>
