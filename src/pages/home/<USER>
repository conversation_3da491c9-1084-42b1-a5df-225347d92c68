<template>
  <div>
    <div class="header-top">
      <div style="display:flex; margin-left:10px;align-items: center;
    font-size: 14px;">
        <div style="color: white;">你好，{{info.name}}</div>
        <van-button style="margin-left:10px" color="#f6d21c" round size="mini">{{info.role_id != 8 ? info.level : info.role}}</van-button>
      </div>
      <div style="display: flex;flex-direction: row;padding: 0px 10px;">
        <div v-if="info && info.promoter_type == 1" class="activity" @click="doctorActivityAction">
          医生活动
        </div>
        <div class="qr" @click="qrcodeAction">
          <van-icon size="15" name="qr" />
          <div>邀请码</div>
        </div>
      </div>
      
    </div>
    <van-tabs v-model="tabbarActive" color="#ff9000" title-active-color="#ff9000" @change="tabsChangeAction">
      <van-tab title="团队统计" name="a" v-if="info && info.level_id != 4" >
        <div class="mid-card" style="height: 182px; margin-top: 20px" v-if="info && info.owner_id == 51">
          <div style="display: flex;justify-content: space-between;">
            <div style="display: flex;align-items: center;">
              <CommonHeader style="padding: 15px 10px" title="团队业绩目标"/>
              <div style="display: flex;justify-content: center;align-items: center;">
                <div style="font-size: 14px; color: gray" v-if="targetQuery.begin_time" @click="showTargetPopup = true">{{targetQuery.begin_time.substring(0,10) }}</div>
                <van-icon name="arrow-down" color="lightgray" />
              </div>
              
            </div>
            <div style="display: flex;align-items: center;margin-right: 10px;">
              <div style="font-size: 14px;color:#ff9000;" @click="tapTargetDetailAction({type : 2})">详情</div>
            </div>
          </div>
          <div v-if="target_list.length > 0">
            <van-swipe :autoplay="3000">
              <van-swipe-item v-for="(item, index) in target_list" :key="index">
                <div style="display: flex;flex-direction: row;justify-content: space-evenly;">
                  <div v-if="index == 0">
                    <van-circle
                      v-model="target_cp_list0"
                      :stroke-width="100"
                      color="#ff9000"
                      layer-color="lightgray"
                      indicator-color="#ff9000"
                      :show-indicators="true"
                      :text="'完成度' + item.complete_percent + '%'"
                    />
                  </div>
                  <div v-if="index == 1">
                    <van-circle
                      v-model="target_cp_list1"
                      :stroke-width="100"
                      color="#ff9000"
                      layer-color="lightgray"
                      indicator-color="#ff9000"
                      :show-indicators="true"
                      :text="'完成度' + item.complete_percent + '%'"
                    />
                  </div>
                  <div v-if="index == 2">
                    <van-circle
                      v-model="target_cp_list2"
                      :stroke-width="100"
                      color="#ff9000"
                      layer-color="lightgray"
                      indicator-color="#ff9000"
                      :show-indicators="true"
                      :text="'完成度' + item.complete_percent + '%'"
                    />
                  </div>
                  <div>
                    <div style="font-size: 15px;font-weight: bold;padding: 10px 0px;">{{item.target_type_str}}</div>
                    <div class="target-title">目标</div>
                    <div class="target-value">{{item.target_type_str == "处方金额" ? "¥" : ""}}{{item.target_amount}}</div>
                    <div class="target-title">完成</div>
                    <div class="target-value">{{item.target_type_str == "处方金额" ? "¥" : ""}}{{item.complete_amount}}</div>
                  </div>
                </div>
              </van-swipe-item>
            </van-swipe>
          </div>
          <div v-else style="padding: 30px; text-align: center; font-size: 24px; color: #f39000;">
            暂未设置
          </div>
        </div>
        <van-tabs style="margin-top: 10px" type="card" color="#ff9000" @click="isPartnerLevelOnClick" v-if="info.role_id == 8" >
          <van-tab title="一级" name=1>
          </van-tab>
          <van-tab title="二级" name=2>
          </van-tab>
        </van-tabs>
        <!-- 业绩数据+医生数据 -->
        <CommonStatisticsView :begin_time="resData.begin_time" :end_time="resData.end_time" :info="info" :isShowTimePicker=1 :counts="teamCounts" :viewType=0 @timePickerAction="teamTimePickerAction" :level="info.role_id == 8 ? partnerQuery.level : undefined" :underline=true :sourceType=2 @tapOrderAction="tapOrderAction" @tapDoctorAction="tapDoctorAction"/>
        <!-- 销售统计数据 -->
        <div class="mid-card" style="height: 132px; margin-top: 20px" v-if="info && info.level_id != 4">
          <div style="display: flex;justify-content: space-between;">
            <div style="display: flex;align-items: center;">
              <CommonHeader style="padding: 15px 10px" title="销售统计"/>
            </div>
            <div style="display: flex;align-items: center;margin-right: 10px;">
              <div style="font-size: 10px; color: gray" v-if="tableQuery.begin_time" @click="showPopup = true">{{tableQuery.begin_time.substring(0,10) + ' 至 ' + tableQuery.end_time.substring(0,10)}}</div>
            </div>
          </div>
          <v-table
            :columns="columns"
            :table-data="tableData"
            :row-height="24"
            is-horizontal-resize
            title-bg-color="#f3f3f3"
            style="width:100%"
            :column-cell-class-name="columnCellClass"
            :show-vertical-border="false">
          </v-table>
        </div>
        <div style="height: 40px"></div>
      </van-tab>
      <van-tab title="业绩统计" v-if="info && info.level_id != 1" name="b">
        <!-- <div class="mid-card" style="height: 182px; margin-top: 20px" v-if="info && info.owner_id == 51"> -->
        <div class="mid-card" style="height: 182px; margin-top: 20px" v-if="info && info.owner_id == 51 ">
          <div style="display: flex;justify-content: space-between;">
            <div style="display: flex;align-items: center;">
              <CommonHeader style="padding: 15px 10px" title="业绩目标"/>
              <div style="display: flex;justify-content: center;align-items: center;">
                <div style="font-size: 14px; color: gray" v-if="targetQuery.begin_time" @click="showTargetPopup = true">{{targetQuery.begin_time.substring(0,10) }}</div>
                <van-icon name="arrow-down" color="lightgray" />
              </div>
             </div>
            <div style="display: flex;align-items: center;margin-right: 10px;">
              <div style="font-size: 14px;color:#ff9000;" @click="tapTargetDetailAction({type : 3})">详情</div>
            </div>
          </div>
          <div v-if="target_list.length > 0">
            <van-swipe :autoplay="3000">
              <van-swipe-item v-for="(item, index) in target_list" :key="index">
                <div style="display: flex;flex-direction: row;justify-content: space-evenly;">
                  <div v-if="index == 0">
                    <van-circle
                      v-model="target_cp_list0"
                      :stroke-width="100"
                      color="#ff9000"
                      layer-color="lightgray"
                      indicator-color="#ff9000"
                      :show-indicators="true"
                      :text="'完成度' + item.complete_percent + '%'"
                    />
                  </div>
                  <div v-if="index == 1">
                    <van-circle
                      v-model="target_cp_list1"
                      :stroke-width="100"
                      color="#ff9000"
                      layer-color="lightgray"
                      indicator-color="#ff9000"
                      :show-indicators="true"
                      :text="'完成度' + item.complete_percent + '%'"
                    />
                  </div>
                  <div v-if="index == 2">
                    <van-circle
                      v-model="target_cp_list2"
                      :stroke-width="100"
                      color="#ff9000"
                      layer-color="lightgray"
                      indicator-color="#ff9000"
                      :show-indicators="true"
                      :text="'完成度' + item.complete_percent + '%'"
                    />
                  </div>
                  <div>
                    <div style="font-size: 15px;font-weight: bold;padding: 10px 0px;">{{item.target_type_str}}</div>
                    <div class="target-title">目标</div>
                    <div class="target-value">{{item.target_type_str == "处方金额" ? "¥" : ""}} {{item.target_amount}}</div>
                    <div class="target-title">完成</div>
                    <div class="target-value">{{item.target_type_str == "处方金额" ? "¥" : ""}} {{item.complete_amount}}</div>
                  </div>
                </div>
              </van-swipe-item>
            </van-swipe>
          </div>
          <div v-else style="padding: 30px; text-align: center; font-size: 24px; color: #f39000;">
            暂未设置
          </div>
        </div>
        <CommonStatisticsView :begin_time="resData.begin_time" :end_time="resData.end_time" :viewType=0 :isShowTimePicker=1 :info="info" :counts="counts" @timePickerAction="teamTimePickerAction" headerTitle="个人业绩数据" bottomTitle="个人医生数据看板" :underline=true :sourceType=1 @tapOrderAction="tapOrderAction" @tapDoctorAction="tapDoctorAction"/>
        <CommonStatisticsView :begin_time="resData.begin_time" :end_time="resData.end_time" :viewType=0 :info="info" :counts="total_team" :isShowTimePicker=0 headerTitle="业绩数据统计" bottomTitle="医生数据统计看板" :underline=true :sourceType=5 @tapOrderAction="tapOrderAction" @tapDoctorAction="tapDoctorAction"/>
      </van-tab>
      <van-tab title="代理商" name="c" v-if="info.is_pharmacies == 1">
        <!-- 代理商统计 -->
        <CommonStatisticsView :begin_time="pharmaciesQuery.begin_time" :end_time="pharmaciesQuery.end_time" :isShowTimePicker=1 :viewType=1 :info="info" :counts="agentDetail" @timePickerAction="agentTimePickerAction" :underline=true :sourceType=3 @tapOrderAction="tapOrderAction" @tapDoctorAction="tapDoctorAction"/>
        <div  class="mid-card" style=" margin-top: 20px; margin-bottom:0px">
          <CommonHeader style="padding: 15px 10px" :title="('代理商数：' + pharmaciesList.length + '个')"/>
          <van-cell v-for="(item, index) in pharmaciesList" :key="index"  :title="item.name"  is-link  @click="clickCell(item)"/>
        </div>
      </van-tab>
      <van-tab title="合伙人" name="d" v-if="info.role_id != 8 && info.is_partners == 1">
        <!-- 合伙人 -->
        <div style="margin-top: 10px">
          <van-tabs type="card" color="#ff9000" @click="partnerLevelOnClick" >
            <van-tab title="一级" name=1>
            </van-tab>
            <van-tab title="二级" name=2>
            </van-tab>
          </van-tabs>
        </div>
        
        <CommonStatisticsView  :isShowTimePicker=1 :begin_time="partnerQuery.begin_time" :end_time="partnerQuery.end_time"  :viewType=0 :info="info" :counts="partnerDetail" :level="partnerQuery.level"  @timePickerAction="partnerTimePickerAction" :underline=true :sourceType=4 @tapOrderAction="tapOrderAction" @tapDoctorAction="tapDoctorAction"/>
        <div  class="mid-card" style=" margin-top: 20px; margin-bottom:0px">
          <!-- <CommonHeader style="padding: 15px 10px" :title="('下级数：' + pharmaciesList.length + '个')"/> -->
          <van-cell v-for="(item, index) in partnerPromoterPartners" :key="index"  :title="item.name"  is-link  @click="partnerClickCell(item)"/>
        </div>
      </van-tab> 
    </van-tabs>
    <van-popup
        v-model="showPopup"
        position="center"
        :style="{ width: '70%' }"
        :close-on-click-overlay = false
        >
        <van-form>
          <van-field
            readonly
            clickable
            name="picker"
            :value="tableQuery.begin_time"
            label="开始时间"
            placeholder="点击选择开始时间"
            @click="showPopupBeginDatePicker = true"
          />
          <van-field
            readonly
            clickable
            name="picker2"
            :value="tableQuery.end_time"
            label="结束时间"
            placeholder="点击选择结束时间"
            @click="showPopupEndDatePicker = true"
          />
        </van-form>
        <div>
          <van-row>
            <van-col span="24">
              <van-button class="width-button" color="#F7941E" type="primary" @click="tableFilterAction">确定</van-button>
            </van-col>
            <!-- <van-col span="12">
              <van-button class="width-button" color="#D4D4D4" type="primary" @click="cleanTableFilterAction">取消</van-button>
            </van-col> -->
          </van-row>
        </div>
      </van-popup>
    <van-popup
      v-model="showPopupBeginDatePicker"
      position="bottom"
    >
      <van-datetime-picker
        type="date"
        :formatter="formatter"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="confirmPopupBeginDatePicker"
        @cancel="showPopupBeginDatePicker = false"
      />
    </van-popup>
    <van-popup
      v-model="showPopupEndDatePicker"
      position="bottom"
    >
      <van-datetime-picker
        type="date"
        :formatter="formatter"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="confirmPopupEndDatePicker"
        @cancel="showPopupEndDatePicker = false"
      />
    </van-popup>
    <van-popup
      v-model="showTargetPopup"
      position="center"
      style="width: 80%"
    >
      <van-datetime-picker
        v-model="maxDate"
        type="year-month"
        title="选择年月"
        :formatter="targetFormatter"
        @confirm="confirmTargetDatePicker"
        @cancel="showTargetPopup = false"
      />
    </van-popup>

    <div style="height: 105px;"></div>
    <router-view />
    <van-tabbar route active-color="#F7941E">
      <van-tabbar-item replace to="/home" icon="home-o">
        首页
      </van-tabbar-item>
      <van-tabbar-item v-if="info && info.level_id != 1" replace to="/doctors" icon="manager-o">
        医生
      </van-tabbar-item>
      <van-tabbar-item v-if="info && info.level_id != 4" replace to="/team" icon="friends-o">
        我的团队
      </van-tabbar-item>
      <van-tabbar-item replace to="/mine" icon="user-circle-o" dot >
        我的
      </van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script>
import { indexInteractor, loginInteractor, partnerInteractor } from '@/core'
import { saveToLocalStorage, readFromLocalStorage, removeToken } from '@/core/services/cache'
import CommonHeader from '@/components/CommonHeader'
import CommonStatisticsView from '@/components/CommonStatisticsView'
import DoctorStatisticsTitle from '@/components/DoctorStatisticsTitle'
import { Toast, Dialog } from 'vant'
import dayjs from 'dayjs'
import quarterOfYear from 'dayjs/plugin/quarterOfYear' // ES 2015
import 'dayjs/locale/zh-cn' // ES 2015 

dayjs.locale('zh-cn')
dayjs.extend(quarterOfYear)

export default {
  name: 'Home',
  components: {
    CommonHeader, DoctorStatisticsTitle, CommonStatisticsView
  },
  data() {
    return {
      target_list: [],
      target_cp_list0: 0,
      target_cp_list1: 0,
      target_cp_list2: 0,
      tabbarActive: undefined,
      agentDetail: undefined,
      pharmaciesList: [],
      resData: undefined,
      counts: undefined,
      total_team: undefined,
      teamCounts: undefined,
      info: undefined,
      partnerDetail: undefined,
      partnerPromoterPartners:[],
      timePicker: 0,
      timeOption: [
        { text: '本月', value: 0 },
        { text: '本日', value: 1 },
        { text: '本周', value: 2 },
        { text: '本季度', value: 3 },
        { text: '上季度', value: 4 },
        { text: '本年度', value: 5 },
      ],
      pharmaciesQuery: {
        begin_time: undefined,
        end_time: undefined
      },
      tableQuery: {
        begin_time: undefined,
        end_time: undefined
      },
      query: {
        begin_time: undefined,
        end_time: undefined
      },
      partnerQuery: {
        begin_time: undefined,
        end_time: undefined,
        level: 1
      },
      targetQuery: {
        type: 1,
        begin_time: undefined
      },
      showTargetPopup: false,
      showPopup: false,
      minDate: new Date(2019, 0, 1),
      maxDate: new Date(),
      showPopupBeginDatePicker: false,
      showPopupEndDatePicker: false,
      show: false,
      tableData: [],
      columns: [
          {field: 'name', title:'姓名', width: 70, titleAlign: 'center',columnAlign:'center', titleCellClassName:'title-cell-class-name-t',},
          {field: 'level', title: '等级', formatter:this.levelFormatter, width: 45, titleAlign: 'center',columnAlign:'center', titleCellClassName:'title-cell-class-name-t'},
          {field: 'amount', title: '订单金额', formatter:this.amountFormatter, width: 90, titleAlign: 'center',columnAlign:'center', titleCellClassName:'title-cell-class-name-t'},
          {field: 'doctors', title: '邀请医生数', width: 70, titleAlign: 'center',columnAlign:'center', titleCellClassName:'title-cell-class-name-t'},
          {field: 'credentials', title: '认证医生数', width: 70, titleAlign: 'center',columnAlign:'center', titleCellClassName:'title-cell-class-name-t'}
      ]
    }
  },
  async created() {
    let now = new Date
    console.log(dayjs(now).quarter() )
    let info = readFromLocalStorage('info')
    console.log(info)
    if (info) {
      this.info = info
    } else {
      this.fetchInfo()
    }
    this.handleIndex()
    this.fetchUpdateAnnouncement()
    this.targetQuery.begin_time = dayjs(Date.now()).format('YYYY-MM')
    this.fetchIndexCRM()
  },
  methods: {
    tabsChangeAction(index) {
      console.log(index)
      if (index == 'a') {
        this.targetQuery.type = 1
        this.fetchIndexCRM()
      } else if (index == 'b') {
        this.targetQuery.type = 2
        this.fetchIndexCRM()
      }
      
    },
    tapTargetDetailAction(p) {
      console.log(p)
      this.$router.push({path:'/targetDetail',query: p})
    },
    confirmTargetDatePicker(q) {
      let monthString = dayjs(q).format('YYYY-MM')
      this.showTargetPopup = false
      console.log(monthString)
      this.targetQuery.begin_time = monthString
      this.fetchIndexCRM()
    },
    targetFormatter(type, val) {
      if (type === 'year') {
        return `${val}年`;
      } else if (type === 'month') {
        return `${val}月`;
      }
      return val;
    },
    fetchIndexCRM() {
      this.target_list = []
      this.target_cp_list = []
      indexInteractor.fetchIndexCRM(this.targetQuery).then(data => {
        // console.log(data.target_list)
        
        this.target_list = data.target_list;

        setTimeout(() => {
          
          this.target_list.map( (e, index) => {
            if (index == 0) {
              this.target_cp_list0 = e.complete_percent
            }
            if (index == 1) {
              this.target_cp_list1 = e.complete_percent
            }
            if (index == 2) {
              this.target_cp_list2 = e.complete_percent
            }
          })
        }, 1000);
      })
    },
    //代理商统计数据
    async fetchUpdateAnnouncement(){
      try {
        const pp = await indexInteractor.fetchUpdateAnnouncement().then(data => {
          if (data.content && data.content.length > 0) {
            Dialog.alert({
              title: '更新公告',
              messageAlign: 'left',
              confirmButtonColor: '#ff9000',
              message: data.content,
            }).then(() => {
              // on close
            });
          }
          
        })
        
      } catch (error) {
        console.log(error)
      }
    },
    tapDoctorAction(p) {
      console.log(p)
      this.$router.push({path:'/statisticsDoctorList',query: p})
    },
    tapOrderAction(p) {
      console.log(p)
      this.$router.push({path:'/orderList',query: p})
    },
    formatter(type, val) {
      if (type === 'year') {
        return `${val}年`;
      } else if (type === 'month') {
        return `${val}月`
      } else {
        return `${val}日`
      }
      return val;
    },
    levelFormatter(rowData,rowIndex,pagingIndex,field) {
      console.log(rowData.level)
      if (this.info.role_id == 8) {
        return rowData.level
      } else {
        switch (rowData.level){
          case 2:
            return '区总'
            break;
          case 3:
            return '地区'
            break;
          case 4:
            return '普通'
            break;
        }
      }
    },
    amountFormatter(rowData,rowIndex,pagingIndex,field) {
      return '¥' + rowData.amount;
    },
    columnCellClass(rowIndex,columnName,rowData){
      return 'column-cell-class-name-t';
    },
    teamTimePickerAction(q) {
      this.handleIndex(q)
    },
    agentTimePickerAction(q) {
      this.fetchAgentIndex(q)
    },
    async fetchInfo() {
      try {
        const info = await indexInteractor.getPromoterInfo()
        const data = Object.assign({}, info)
        this.info = data
        saveToLocalStorage('info', data)
        this.handleIsPartnerTeamIndex()
      } catch (error) {
        console.log(error)
      }
    },
    //合伙人统计数据
    async fetchPartnerTeam(q){
      try {
        const pp = await partnerInteractor.fetchPartnerTeam(q)
        const data = Object.assign({}, pp)
        this.partnerDetail = data
      } catch (error) {
        console.log(error)
      }
    },
    //合伙人下级列表数据
    async fetchPromoterPartners(q){
      try {
        const pp = await partnerInteractor.fetchPromoterPartners(q)
        const data = Object.assign({}, pp)
        this.partnerPromoterPartners = data.list
      } catch (error) {
        console.log(error)
      }
    },
    //合伙人切换等级 
    partnerLevelOnClick(name, title) {
      this.partnerQuery.level = name;
      this.fetchPartnerTeam(this.partnerQuery)
      this.fetchPromoterPartners({level: name})
    },
    //合伙人切换时间区间
    partnerTimePickerAction(q) {
      this.partnerQuery.begin_time = q.begin_time
      this.partnerQuery.end_time = q.end_time
      this.fetchPartnerTeam(this.partnerQuery)
    },
    //登录合伙人切换等级
    isPartnerLevelOnClick(name, title) {
      this.partnerQuery.level = name
      this.handleIsPartnerTeamIndex()
      this.fetchPartnerPeport(this.tableQuery)
    },
    //合伙人点击下级
    partnerClickCell(item) {
      item['level_id'] = this.partnerQuery.level
      this.$router.push({path:'/partnerDetail',query: item})
    },
    //代理商统计数据
    async fetchAgentIndex(q){
      try {
        const pp = await indexInteractor.getAgentIndex(q)
        const data = Object.assign({}, pp)
        this.agentDetail = data.team
        this.pharmaciesQuery.begin_time = data.begin_time
        this.pharmaciesQuery.end_time = data.end_time
        this.pharmaciesList = data.pharmacies.pharmacies
      } catch (error) {
        console.log(error)
      }
    },
    async fetchPartnerPeport(q){
      if (this.info.role_id == 8) {
        try {
          let qq = Object()
          if (!q) {
            qq.level = this.partnerQuery.level
          } else {
            q['level'] = this.partnerQuery.level
            qq = q
          }
          const pp = await partnerInteractor.fetchPartnerSalesReport(qq)
          const data = Object.assign({}, pp)
          this.tableData = data.list
        } catch (error) {
          console.log(error)
        }
      } else {
        try {
          const pp = await indexInteractor.getPartnerPeport(q)
          const data = Object.assign({}, pp)
          this.tableData = data.items
        } catch (error) {
          console.log(error)
        }
      }
    },
    async handleIndex(query) {
      try {
        const test = await indexInteractor.getIndex(query)
        const data = Object.assign({}, test)
        this.resData = data
        this.teamCounts = data.team
        this.counts = data.self
        this.total_team = data.total_team
        this.partnerQuery.begin_time = data.begin_time
        this.partnerQuery.end_time = data.end_time
        this.$bus.emit('new_sysmessage_count', data.new_sysmessage_count)
        this.targetQuery.begin_time = dayjs(data.begin_time).format('YYYY-MM')
        if (!query) {
          this.tableQuery.begin_time = data.begin_time
          this.tableQuery.end_time = data.end_time
          this.pharmaciesQuery.begin_time = data.begin_time
          this.pharmaciesQuery.end_time = data.end_time
          this.fetchPartnerTeam(this.partnerQuery)
          this.fetchPromoterPartners({'level': this.partnerQuery.level})
          this.fetchPartnerPeport(query)
          this.fetchAgentIndex(query)
        }
        if (this.info && this.info.role_id == 8) {
          this.handleIsPartnerTeamIndex()
        }
      } catch (error) {
        console.log(error)
      }
    },
    async handleIsPartnerTeamIndex() {
      try {
        const test = await partnerInteractor.fetchPartnerTeam(this.partnerQuery)
        const data = Object.assign({}, test)
        this.teamCounts = data
      } catch (error) {
        console.log(error)
      }
    },
    doctorActivityAction() {
      this.$router.push('/doctorActivities')
    },
    qrcodeAction() {
      this.$router.push('/qrcode')
    },
    clickCell(item) {
      this.$router.push({path:'/pharmacyDetail',query: item})
    },
    confirmPopupBeginDatePicker(t) {
      
      let t0 = dayjs(t).format('YYYY-MM-DD')
      this.tableQuery.begin_time = t0
      this.showPopupBeginDatePicker = false
    },
    confirmPopupEndDatePicker(t) {
      let t1 = dayjs(t).format('YYYY-MM-DD')
      this.tableQuery.end_time = t1
      this.showPopupEndDatePicker = false
    },
    tableFilterAction() {
      this.fetchPartnerPeport(this.tableQuery)
      this.showPopup = false
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .title-cell-class-name-t{
  background-color: #f3f3f3;
  font-size: 10px;
}
::v-deep .column-cell-class-name-t{
  font-size: 10px;
}

::v-deep .van-dropdown-item--down {
  margin: 0px 6% !important;
}
.header-top {
  display:flex;
  align-items: center;
  background-color: #ff9000;
  justify-content: space-between;
}
.info-view {
  margin-left: 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.info-title {
  display: flex;
  align-items: center;
}
.header-view {
  width: 70%;
  padding: 30px 10px;
  display: flex;
  align-items: center;
}
.qr {
  /* text-align: center; */
  margin-top: 0px;
  color: white;
  justify-content: center;
  align-items: center;
  display: flex;
  font-size: 12px;
  line-height: 40px;
}

.activity {
  /* text-align: center; */
  padding: 0px 10px;
  margin-top: 0px;
  color: white;
  justify-content: center;
  align-items: center;
  display: flex;
  font-size: 12px;
  line-height: 40px;
}
.mid-card {
  margin: 0px -6% 0px 6%;
  width: 88%;
  background-color: white;
  border-radius: 5px;
}
.van-cell {
  background-color: #fcfcfc;
  margin: 0px 2% 0px 2%;
  border-radius: 5;
  width: 96%;
}
.cell-value {
  color: black;
  font-weight: bold;
}
.target-title {
  font-size: 13px;
  color: gray;
  // padding: 5px 0px;
}
.target-value {
  font-size: 16px;
  padding: 5px 0px;
}
::v-deep .van-swipe__indicator--active {
  background-color: #ff9000 !important;
}

::v-deep .van-swipe__indicator {
  background-color: gray;
}
::v-deep .van-circle__text {
  left: 25%;
  width: 50%;
  font-size: 12px;
}
</style>
