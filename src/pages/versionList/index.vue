<template>
  <div>
    <van-pull-refresh v-model="loading" @refresh="onRefresh" class="list-container">
      <van-list
        v-model="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
        offset="150"
      >
      <van-panel :icon="item.type == 1 ? require('../../assets/android.jpg') : require('../../assets/apple.jpg') " :title="'版本：' + item.version_num"  :status="(item.created_at)" v-for="item in list" :key="item.id">
        <div style="padding:10px" v-html="item.update_detail"></div>
      </van-panel>
      </van-list>
    </van-pull-refresh>
  </div>
</template>

<script>
import { indexInteractor } from '@/core'
export default {
  name: 'VersionList',
  props: {},
  data() {
    return {
      list: [],
      loading: false,
      finished: false,
      refreshing: false,
      total: 0,
      query: {
        page: 0,
        size: 5
      },
    }
  },
  created() {},
  mounted() {},
  methods: {
    async fetchVersionList(query) {
      try {
        // let d = this.d
        let q = Object.assign({}, query, query)
        console.log(q)
        await indexInteractor.getVersionList(query).then(data => {
          console.log(data)
          if (this.query.page === 1) {
            this.list = data.list
          } else {
            this.list = [...this.list, ...data.list]
          }
          if (data.total === this.list.length) {
            this.finished = true
          } else {
            this.finished = false
          }
          this.loading = false
          this.total = data.total
        })
      } catch (error) {
        console.log(error)
      }
    },
    onLoad() {
      this.loading = true
      this.query.page = this.query.page + 1
      this.fetchVersionList(this.query)
    },
    onRefresh() {
      // 清空列表数据
      this.finished = false
      this.loading = true
      this.query.page = 1
      this.list = []
      this.fetchVersionList(this.query)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .van-cell__title {
  font-size: 14px;
  font-weight: bold;
}
::v-deep .van-panel__header-value {
  color: gray;
}
</style>
