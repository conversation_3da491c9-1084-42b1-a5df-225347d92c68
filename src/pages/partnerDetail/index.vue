<template>
  <div>
    <div class="header-top">
      <div class="info-title">
        <div style="color: white;">{{info.name}}</div>
        <van-button style="margin-left:10px" color="#f6d21c" round size="mini">{{info.level}}</van-button>
      </div>
      <div style="padding-top: 8px;font-size: 12px;display: flex;">
        <div v-if="info.parent_name" style="color: white; margin-right: 10px;">我的上级:{{info.parent_name}}</div>
        <div style="color: white;">所属地区:{{info.area}}</div>
      </div>
    </div>
    <van-tabs v-model="active" color="#F7941E">
      <van-tab title="个人业绩统计">
        <div>
          <div class="mid-card" style="height: 120px; margin-top: 20px">
            <div style="display: flex;justify-content: space-between;">
              <CommonHeader style="padding: 15px 10px" title="订单业绩"/>
              <div>
                <div style="display: flex;align-items: center;margin-right: 10px;">
                  <div style="font-size: 10px; color: gray" v-if="counts">{{counts.begin_time.substring(0,10) + ' 至 ' + counts.end_time.substring(0,10)}}</div>
                  <van-dropdown-menu>
                    <van-dropdown-item v-model="timePicker" :options="timeOption" />
                  </van-dropdown-menu>
                </div>
              </div>
            </div>
            <van-row type="flex" justify="space-around" v-if="counts">
              <van-col span="11"><DoctorStatisticsTitle title="订单笔数" :count="counts.self.orders.order_number"/></van-col>
              <van-col span="11"><DoctorStatisticsTitle title="订单金额" :count="counts.self.orders.order_total_amount"/></van-col>
            </van-row>
          </div>
          <div class="mid-card" style="height: 132px; margin-top: 20px">
            <CommonHeader style="padding: 15px 10px" title="医生数据看板"/>
            <van-row type="flex" justify="space-around" v-if="counts">
              <van-col span="5"><DoctorStatisticsTitle title="邀请医生数" :count="counts.self.doctors.doctors"/></van-col>
              <van-col span="5"><DoctorStatisticsTitle title="已认证医生" :count="counts.self.doctors.credentials"/></van-col>
              <van-col span="5"><DoctorStatisticsTitle title="未认证医生" :count="counts.self.doctors.not_credentials"/></van-col>
              <van-col span="6"><DoctorStatisticsTitle title="认证拒绝医生" :count="counts.self.doctors.refuse_credentials"/></van-col>
            </van-row>
          </div>
        </div>
      </van-tab>
      <!-- <van-tab title="团队统计">
        <div>
          <div class="mid-card" style="height: 120px; margin-top: 20px">
            <div style="display: flex;justify-content: space-between;">
              <CommonHeader style="padding: 15px 10px" title="订单业绩"/>
              <div>
                <div style="display: flex;align-items: center;margin-right: 10px;">
                  <div style="font-size: 10px; color: gray" v-if="counts">{{counts.begin_time.substring(0,10) + ' 至 ' + counts.end_time.substring(0,10)}}</div>
                  <van-dropdown-menu>
                    <van-dropdown-item v-model="timePicker" :options="timeOption" />
                  </van-dropdown-menu>
                </div>
              </div>
            </div>
            <van-row type="flex" justify="space-around" v-if="counts">
              <van-col span="11"><DoctorStatisticsTitle title="订单笔数" :count="counts.team.orders.order_number"/></van-col>
              <van-col span="11"><DoctorStatisticsTitle title="订单金额" :count="counts.team.orders.order_total_amount"/></van-col>
            </van-row>
          </div>
          <div class="mid-card" style="height: 132px; margin-top: 20px">
            <CommonHeader style="padding: 15px 10px" title="医生数据看板"/>
            <van-row type="flex" justify="space-around" v-if="counts">
              <van-col span="5"><DoctorStatisticsTitle title="邀请医生数" :count="counts.team.doctors.doctors"/></van-col>
              <van-col span="5"><DoctorStatisticsTitle title="已认证医生" :count="counts.team.doctors.credentials"/></van-col>
              <van-col span="5"><DoctorStatisticsTitle title="未认证医生" :count="counts.team.doctors.not_credentials"/></van-col>
              <van-col span="6"><DoctorStatisticsTitle title="认证拒绝医生" :count="counts.team.doctors.refuse_credentials"/></van-col>
            </van-row>
          </div>
        </div>
      </van-tab> -->
    </van-tabs>
  </div>
</template>

<script>
import { partnerInteractor } from '@/core'
import CommonHeader from '@/components/CommonHeader'
import DoctorStatisticsTitle from '@/components/DoctorStatisticsTitle'
import { Toast } from 'vant'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn' // ES 2015 

dayjs.locale('zh-cn')
export default {
  name: 'PartnerDetail',
  props: {},
  components: {
    CommonHeader, DoctorStatisticsTitle
  },
  data() {
    return {
      active: 0,
      minDate: new Date(2019, 0, 1),
      maxDate: new Date(),
      info: undefined,
      query: {
        level: 1,
        promoter_id: undefined,
        begin_time: undefined,
        end_time: undefined 
      },
      begin_time: undefined,
      end_time: undefined,
      timePicker: 0,
      counts: undefined,
      timeOption: [
        { text: '本月', value: 0 },
        { text: '本日', value: 1 },
        { text: '本周', value: 2 },
        { text: '本季度', value: 3 },
        { text: '上季度', value: 4 },
        { text: '本年度', value: 5 },
      ],
    }
  },
  watch: {
    timePicker: function (val, oldVal) {
      console.log('new a: %s, old a: %s', val, oldVal)
      let b,e
      let now = new Date
      switch (val) {
          case 0:
              b = new Date(now.getFullYear(), now.getMonth(), 1)
              e = now
              break;
          case 1:
              b = now
              e = now
              break;
          case 2:
              let w = dayjs(now).day(1)
              b = w.$d
              e = now
              console.log(w)
              break;
          case 3:
              let q = dayjs(now).quarter()
              let d = dayjs(now.getFullYear().toString()).quarter(q)
              b = d.$d
              e = now
              break;
          case 4:
              let qq = dayjs(now).quarter()
              let dd = dayjs(now.getFullYear().toString()).quarter(qq - 1)
              let ww = dayjs(now.getFullYear().toString()).quarter(qq - 1).endOf('quarter')
              b = dd.$d
              e = ww.$d
              console.log(dayjs(e).format('YYYY-MM-DD'))
              break;
          case 5:
              b = new Date(now.getFullYear(),0,1,0,0,0)
              e = now
              break;
      }
      this.query.begin_time = dayjs(b).format('YYYY-MM-DD') 
      this.query.end_time = dayjs(e).format('YYYY-MM-DD') 
      this.fetchPromoterPartnersIndex(this.query)
    },
  },
  created() {
    this.query.promoter_id = this.$route.query.id
    this.query.level = this.$route.query.level_id
    this.info = this.$route.query
    this.fetchPromoterPartnersIndex(this.query)
  },
  mounted() {},
  methods: {
    //合伙人统计数据
    async fetchPromoterPartnersIndex(q){
      try {
        const pp = await partnerInteractor.fetchPromoterPartnersIndex(q)
        const data = Object.assign({}, pp)
        this.counts = data
      } catch (error) {
        console.log(error)
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.info-title {
  display: flex;
  align-items: center;
  justify-content: center;
}
.header-top {
  align-items: center;
  display: flex;
  flex-direction: column;
  height: 80px;
  background-color: #ff9000;
  justify-content: center;
}
::v-deep .van-dropdown-item--down {
  margin: 0px 6% !important;
}
.cell {
  background-color: white;
  margin: 10px;
  height: 88px;
  border-radius: 10px;
  display: flex;
}
.mid-card {
  margin: 0px -6% 0px 6%;
  width: 88%;
  background-color: white;
  border-radius: 5px;
}
.van-cell {
  background-color: #fcfcfc;
  margin: 0px 2% 0px 2%;
  border-radius: 5;
  width: 96%;
}
.cell-value {
  color: black;
  font-weight: bold;
}
</style>
