<template>
  <div>
    <div style="line-height: 40px;background-color: white;padding-left: 20px;font-size: 14px;">统计日期： {{d.begin_time + '至' + d.end_time}}</div>
    <div style="line-height: 30px;padding-left: 20px;color: gray; font-size: 13px;">订单笔数：{{total}}</div>
    <van-pull-refresh v-model="loading" @refresh="onRefresh" class="list-container">
      <van-list
        v-model="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
        offset="150"
      >
      <van-panel :title="('医生:' + item.doctor_name + ' / 患者:' + item.archive_name)"  :status="(item.shipping_status)" v-for="item in list" :key="item.no" style="margin: 0 10px 10px 10px">
        <van-grid :column-num="2" :center=false :border=false>
          <van-grid-item :text="'订单单号：' + item.order_no" />
          <van-grid-item :text="'处方单金额：' + item.amount" />
          <van-grid-item :text="(item.pharmacy_type == 2 ? '订单金额：' : '基础药费：') + item.medicine_charge" />
          <van-grid-item :text="'日期：' + item.created_at" />
          <van-grid-item v-if="item.express_no" >
            <div style="display: flex;font-size: 12px; color: #646566">
              <div>快递单号：</div>
              <a style="color:blue" :href="item.show_express">{{item.express_no}}</a>
            </div>
          </van-grid-item>
        </van-grid>
      </van-panel>
      </van-list>
    </van-pull-refresh>
  </div>
</template>

<script>
import { orderInteractor } from '@/core'
export default {
  name: 'OrderList',
  props: {},
  data() {
    return {
      d: undefined,
      list: [],
      loading: false,
      finished: false,
      refreshing: false,
      total: 0,
      query: {
        page: 0,
        size: 10
      },
    }
  },
  created() {
    this.d = this.$route.query
    console.log(this.d)
  },
  mounted() {},
  methods: {
    async fetchOrdersList(query) {
      try {
        let d = this.d
        let q = Object.assign(d, query, query)
        console.log(q)
        await orderInteractor.fetchOrderReportList(q).then(data => {
          console.log(data)
          if (this.query.page === 1) {
            this.list = data.list
          } else {
            this.list = [...this.list, ...data.list]
          }
          if (data.total === this.list.length) {
            this.finished = true
          } else {
            this.finished = false
          }
          this.loading = false
          this.total = data.total
        })
      } catch (error) {
        console.log(error)
      }
    },
    onLoad() {
      // 异步更新数据
      // setTimeout 仅做示例，真实场景中一般为 ajax 请求
      this.loading = true
      this.query.page = this.query.page + 1
      console.log('onLoad')
      this.fetchOrdersList(this.query)
    },
    onRefresh() {
      // 清空列表数据
      this.finished = false
      console.log('onRefresh')
      // 重新加载数据
      // 将 loading 设置为 true，表示处于加载状态
      this.loading = true
      this.query.page = 1
      this.list = []
      this.fetchOrdersList(this.query)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .van-cell {
  font-size: 12px;
  padding: 10px;
}
::v-deep .van-panel__header-value {
  color: #ff9000;
}
</style>
