<template>
  <div class="content">
    <div>
      <div style="display: flex;justify-content: center;">
        <!-- <img
          class="headerImg"
          src="~@/assets/xlz.jpg"
        /> -->
        <van-image class="headerImg" :src="require('../../assets/xlz.jpg')" />
      </div>
      
      <van-cell-group>
        <van-field
          v-model="username"
          label=""
          left-icon="user-circle-o"
          placeholder="请输入中文姓名或手机号码"
          size="large"
        />
        <van-field
          v-model="password"
          clearable
          label=""
          left-icon="more-o"
          type="password"
          placeholder="登录密码"
          size="large"
        />
      </van-cell-group>
      <div class="forgot">
        <p @click="forgotAction" style="color: #F7941E">忘记密码</p>
      </div>
      <div class="bottom-button">
         <van-button class="v-button" round color="linear-gradient(to right, #F7941E, #F24316)" @click="handleLogin" >登录</van-button>
      </div>
    </div>
  </div>
</template>

<script>
import { loginInteractor, indexInteractor } from '@/core'
import { setToken } from '@/core/services/cache'
import { saveToLocalStorage } from '@/core/services/cache'
import { Toast, Dialog } from 'vant'
export default {
  name: 'Login',
  props: {},
  data() {                                                                      
    return {
      redirect: undefined,
      username: undefined,
      password: undefined
      
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    }
  },
  created() {},
  mounted() {},
  methods: {
    async handleLogin() {
      if (!this.username) {
        Toast.fail('请输入中文姓名或手机号码')
        return
      }
      if (!this.password) {
        Toast.fail('请输入密码')
        return
      }
      try {
        const test = await loginInteractor.fetchLogin({'username': this.username, 'password': this.password})
        let data = Object.assign({}, test)
        console.log(data.token)
        setToken(data.token)
        const info = await indexInteractor.getPromoterInfo()
        const pdata = Object.assign({}, info)
        saveToLocalStorage('info', pdata)
        this.$router.push({ path: this.redirect || '/' })
      } catch (error) {
        console.log(error)
      }
    },
    forgotAction() {
      this.$router.push('/forgot')
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .van-cell {
  font-size: 16px;
  line-height: 44px;
}
::v-deep .van-field__left-icon .van-icon {
  font-size: 22px;
}
::v-deep .van-field__control {
  padding-left: 10px;
}
.headerImg {
  width: 60%;
  margin: 100px 20px 50px 20px;
}
.content {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.bottom-button {
  margin-top: 50px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  width: 100%;
}
.forgot {
  text-align: center;
}
.v-button {
  width: 200px !important; 
}
</style>
