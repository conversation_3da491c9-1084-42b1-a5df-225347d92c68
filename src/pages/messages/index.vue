<template>
  <div>
     <van-pull-refresh v-model="loading" @refresh="onRefresh">
        <van-list
          v-model="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
          offset="150"
        >
        <div class="cell" v-for="item in list" :key="item.id" @click="onClick(item)">
          <van-cell-group inset>
            <van-cell :label="item.content" >
              <template #title>
                <div style="display: flex;flex-direction: row;justify-content: space-between;">
                  <div>{{item.title}}</div>
                  <div style="font-size:12px;color:gray">{{item.created_at}}</div>
                </div>
              </template>
              <template #icon>
                <van-icon :dot="item.status == 0" name="comment-o" size="30" style="line-height: 46px;padding: 0px 4px;"/>
              </template>
            </van-cell>
          </van-cell-group>
        </div>
        </van-list>
      </van-pull-refresh>
  </div>
</template>

<script>

import { indexInteractor } from '@/core'
export default {
  name: 'Messages',
  props: {},
  data() {
    return {
      list: [],
      loading: false,
      finished: false,
      refreshing: false,
      query: {
        page: 0,
        size: 10,
      },
    }
  },
  created() {},
  mounted() {

  },
  methods: {
    onLoad() {
      this.loading = true
      this.query.page = this.query.page + 1
      this.getSystemMessageList(this.query)
    },
    onRefresh() {
      // 清空列表数据
      this.finished = false
      this.loading = true
      this.query.page = 1
      this.list = []
      this.getSystemMessageList(this.query)
    },
    async getSystemMessageList(query){
      try {
        let q = Object.assign({}, query, query)
        await indexInteractor.getSystemMessageList(q).then(data => {
          if (this.query.page === 1) {
            this.list = data.list
          } else {
            this.list = [...this.list, ...data.list]
          }
          if (data.total <= this.list.length) {
            this.finished = true
          }
          console.log(this.list)
          this.loading = false
        })
      } catch (error) {
        console.log(error)
      }
    },
    onClick(item) {
      if (item.doctor_id) {
        indexInteractor.fetchSystemMessageRead({id: item.id}).then(data => {
          this.$router.push({path:'/doctorDetail',query: { doctor_id : item.doctor_id}})
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.cell {
  margin-top: 10px;
}
</style>
