<template>
  <div>
    <van-form input-align="right">
      <van-field name="switch" :center=true >
        <template #label>
          <div style="display: flex;flex-direction: column;justify-content: space-between;">
            <div>消息提醒</div>
            <div style="font-size:12px;color:gray">关闭按钮后，首页通知将不会显示红点提示</div>
          </div>
        </template>
        <template #input>
          <van-switch v-model="form.message_notice" size="20" active-color="#ff9000" :active-value=1 :inactive-value=0 @change="switchChange(0)"/>
        </template>
      </van-field>
      <van-field name="switch" :center=true >
        <template #label>
          <div style="display: flex;flex-direction: column;justify-content: space-between;">
            <div>短信提醒</div>
            <div style="font-size:12px;color:gray">关闭按钮后，将不会接收到消息短信提醒</div>
          </div>
        </template>
        <template #input>
          <van-switch v-model="form.sms_notice" size="20" active-color="#ff9000" :active-value=1 :inactive-value=0  @change="switchChange(1)"/>
        </template>
      </van-field>
    </van-form>
  </div>
</template>

<script>

import { indexInteractor } from '@/core'
export default {
  name: 'messageSettings',
  props: {},
  data() {
    return {
      form: {
        message_notice: 0,
        sms_notice: 0
      }
    }
  },
  created() {},
  mounted() {
    this.fetchConfigList()
  },
  methods: {
    async fetchConfigList(){
      try {
        
        await indexInteractor.systemMessageConfigList().then(data => {
          this.form = data.config
        })
      } catch (error) {
        console.log(error)
      }
    },
    async switchChange(type){
      try {
        var query;
        if (type == 0) {
          query = {field: 'message_notice', value: this.form.message_notice}
        } else {
          query = {field: 'sms_notice', value: this.form.sms_notice}
        }
        let q = Object.assign({}, query, query)
        await indexInteractor.systemMessageConfigSave(q).then(data => {
          
        })
      } catch (error) {
        console.log(error)
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.cell {
  margin-top: 10px;
}
::v-deep .van-cell__title {
  width: 290px;
}
</style>
