<template>
  <div>
    <van-tabs v-model="active" color="#F7941E">
      <van-tab title="代理商统计">
        <div>
          <div class="mid-card" style="height: 120px; margin-top: 20px">
            <div style="display: flex;justify-content: space-between;">
              <CommonHeader style="padding: 15px 10px" title="订单业绩"/>
              <div>
                <div style="display: flex;align-items: center;margin-right: 10px;">
                  <div style="font-size: 10px; color: gray" v-if="counts">{{counts.begin_time.substring(0,10) + ' 至 ' + counts.end_time.substring(0,10)}}</div>
                  <van-dropdown-menu>
                    <van-dropdown-item v-model="timePicker" :options="timeOption" />
                  </van-dropdown-menu>
                </div>
              </div>
            </div>
            <van-row type="flex" justify="space-around" v-if="counts">
              <van-col span="11"><DoctorStatisticsTitle title="订单笔数" :count="counts.orders.order_number"/></van-col>
              <van-col span="11"><DoctorStatisticsTitle title="订单金额" :count="counts.orders.order_total_amount"/></van-col>
            </van-row>
            <!-- <van-cell style="margin-top:10px" title="药费金额" value-class="cell-value" :value="'¥' + counts.orders.base_medicine_charge" v-if="counts"/> -->
            <!-- <van-cell title="纯销金额" value-class="cell-value" :value="'¥' + counts.orders.order_amount" v-if="counts"/>
            <van-cell title="奖金预估" value-class="cell-value" :value="'¥' + counts.orders.bonus_predict" v-if="counts"/> -->
          </div>
          <div class="mid-card" style="height: 132px; margin-top: 20px">
            <CommonHeader style="padding: 15px 10px" title="医生数据看板"/>
            <van-row type="flex" justify="space-around" v-if="counts">
              <van-col span="5"><DoctorStatisticsTitle title="邀请医生数" :count="counts.doctors.doctors"/></van-col>
              <van-col span="5"><DoctorStatisticsTitle title="已认证医生" :count="counts.doctors.credentials"/></van-col>
              <van-col span="5"><DoctorStatisticsTitle title="未认证医生" :count="counts.doctors.not_credentials"/></van-col>
              <van-col span="6"><DoctorStatisticsTitle title="认证拒绝医生" :count="counts.doctors.refuse_credentials"/></van-col>
            </van-row>
          </div>
        </div>
      </van-tab>
      <!-- <van-tab title="医生">
        <div> -->
          <!-- <div style="display: flex;align-items: center;">
            <van-search
              v-model="query.keywords"
              placeholder="请输入医生姓名"
              @search="onSearch"
              style="width: 80%;"
            />
            <van-button type="default" style="width: 20%;border: 0px;height: 54px;" @click="showPopup = true">筛选</van-button>
          </div> -->
          <!-- <van-pull-refresh v-model="loading" @refresh="onRefresh">
            <van-list
              v-model="loading"
              :finished="finished"
              finished-text="没有更多了"
              @load="onLoad"
              offset="150"
            >
            <div class="cell" v-for="item in list" :key="item.doctor_id">
              <AvatarButton :url="item.figure_file ? item.figure_file.url : ''" :statusString="item.status_name" :status="item.status"/>
              <div style="width:100%">
                <van-cell :title="((item.name ? item.name : '') + ' ' + item.mobile)" is-link @click="clickCell(item)" />
                <van-cell :title="'对接：' + item.promoter_name" :value="item.created_at.split(' ')[0] + '邀请'" />
              </div>
            </div>
            </van-list>
          </van-pull-refresh>
          <van-popup
            v-model="showPopup"
            position="top"
            :close-on-click-overlay = false
            >
            <van-form>
              <van-field
                readonly
                clickable
                name="picker"
                :value="query.begin_time ? query.begin_time.toLocaleDateString() : ''"
                label="开始时间"
                placeholder="点击选择开始时间"
                @click="showPopupBeginDatePicker = true"
              />
              <van-field
                readonly
                clickable
                name="picker2"
                :value="query.end_time ? query.end_time.toLocaleDateString() : ''"
                label="结束时间"
                placeholder="点击选择结束时间"
                @click="showPopupEndDatePicker = true"
              />
              <van-field name="radio" label="医生状态">
                <template #input>
                  <van-radio-group v-model="query.status" direction="horizontal">
                    <van-radio style="height: 30px" name="-1" checked-color="#F7941E">全部</van-radio>
                    <van-radio style="height: 30px" name="0" checked-color="#F7941E">未认证</van-radio>
                    <van-radio style="height: 30px" name="1" checked-color="#F7941E">已认证</van-radio>
                    <van-radio style="height: 30px" name="2" checked-color="#F7941E">拒绝</van-radio>
                  </van-radio-group>
                </template>
              </van-field>
            </van-form>
            <div>
              <van-row>
                <van-col span="12">
                  <van-button class="width-button" color="#F7941E" type="primary" @click="filterAction">确定</van-button>
                </van-col>
                <van-col span="12">
                  <van-button class="width-button" color="#D4D4D4" type="primary" @click="cleanFilterAction">清除条件</van-button>
                </van-col>
              </van-row>
            </div>
          </van-popup>
          <van-popup
            v-model="showPopupBeginDatePicker"
            position="bottom"
          >
            <van-datetime-picker
              v-model="begin_time"
              type="date"
              :formatter="formatter"
              :min-date="minDate"
              :max-date="maxDate"
              @confirm="confirmPopupBeginDatePicker"
              @cancel="showPopupBeginDatePicker = false"
            />
          </van-popup>
          <van-popup
            v-model="showPopupEndDatePicker"
            position="bottom"
          >
            <van-datetime-picker
              v-model="end_time"
              type="date"
              :formatter="formatter"
              :min-date="minDate"
              :max-date="maxDate"
              @confirm="confirmPopupEndDatePicker"
              @cancel="showPopupEndDatePicker = false"
            />
          </van-popup>
        </div>
      </van-tab> -->
    </van-tabs>
  </div>
</template>

<script>
import { pharmacyInteractor } from '@/core'
import CommonHeader from '@/components/CommonHeader'
import DoctorStatisticsTitle from '@/components/DoctorStatisticsTitle'
import AvatarButton from '@/components/AvatarButton'
import { Toast } from 'vant'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn' // ES 2015 

dayjs.locale('zh-cn')
export default {
  name: 'PharmacyDetail',
  props: {},
  components: {
    AvatarButton, CommonHeader, DoctorStatisticsTitle
  },
  data() {
    return {
      active: 0,
      list: [],
      query: {
        page: 0,
        size: 7,
        status: '-1',
        keywords: '',
        pharmacy_id: undefined,
        begin_time: undefined,
        end_time: undefined 
      },
      begin_time: undefined,
      end_time: undefined,
      showPopup: false,
      minDate: new Date(2019, 0, 1),
      maxDate: new Date(),
      showPopupBeginDatePicker: false,
      showPopupEndDatePicker: false,
      loading: false,
      finished: false,
      refreshing: false,
      counts: undefined,
      countsQuery: {
        pharmacy_id: undefined,
        begin_time: undefined,
        end_time: undefined
      },
      timePicker: 0,
      timeOption: [
        { text: '本月', value: 0 },
        { text: '本日', value: 1 },
        { text: '本周', value: 2 },
        { text: '本季度', value: 3 },
        { text: '上季度', value: 4 },
        { text: '本年度', value: 5 },
      ],
    }
  },
  beforeCreate () {
    // this.$route.meta.title = '6666'
  },
  watch: {
    timePicker: function (val, oldVal) {
      console.log('new a: %s, old a: %s', val, oldVal)
      let b,e
      let now = new Date
      switch (val) {
          case 0:
              b = new Date(now.getFullYear(), now.getMonth(), 1)
              e = now
              break;
          case 1:
              b = now
              e = now
              break;
          case 2:
              let w = dayjs(now).day(1)
              b = w.$d
              e = now
              console.log(w)
              break;
          case 3:
              let q = dayjs(now).quarter()
              let d = dayjs(now.getFullYear().toString()).quarter(q)
              b = d.$d
              e = now
              break;
          case 4:
              let qq = dayjs(now).quarter()
              let dd = dayjs(now.getFullYear().toString()).quarter(qq - 1)
              let ww = dayjs(now.getFullYear().toString()).quarter(qq - 1).endOf('quarter')
              b = dd.$d
              e = ww.$d
              console.log(dayjs(e).format('YYYY-MM-DD'))
              break;
          case 5:
              b = new Date(now.getFullYear(),0,1,0,0,0)
              e = now
              break;
      }
      this.countsQuery.begin_time = dayjs(b).format('YYYY-MM-DD') 
      this.countsQuery.end_time = dayjs(e).format('YYYY-MM-DD') 
      this.handleCounts(this.countsQuery)
    },
  },
  created() {
    console.log(this.$route)
    this.query.pharmacy_id = this.$route.query.id
    this.countsQuery.pharmacy_id = this.$route.query.id
    this.handleCounts(this.countsQuery)
  },
  mounted() {},
  methods: {
    formatter(type, val) {
      if (type === 'year') {
        return `${val}年`;
      } else if (type === 'month') {
        return `${val}月`
      } else {
        return `${val}日`
      }
      return val;
    },
    onSearch(k) {
      console.log(k)
      this.query.page = 1
      this.list = []
      // this.getDoctorsList(this.query)
    },
    clickCell(item) {
      if (item.status == 0 ) {
        Toast.fail('该医生未认证')
      } else if (item.status == 2) {
        Toast.fail('该医生认证已拒绝')
      } else {
        this.$router.push({path:'/doctorDetail',query: item})
      }
    },
    confirmPopupBeginDatePicker(t) {
      this.query.begin_time = t
      this.showPopupBeginDatePicker = false
    },
    confirmPopupEndDatePicker(t) {
      this.query.end_time = t
      this.showPopupEndDatePicker = false
    },
    filterAction() {
      this.showPopup = false
      this.query.page = 1
      this.list = []
      // this.getDoctorsList(this.query)
    },
    cleanFilterAction() {
      this.end_time = undefined
      this.begin_time = undefined
      this.query.begin_time = undefined
      this.query.end_time = undefined
      this.showPopup = false
      this.list = []
      this.query = {
        page: 1,
        size: 7,
        status: '-1',
        pharmacy_id: this.query.id,
        keywords: this.query.keywords
      }
      this.getDoctorsList(this.query)
    },
    onLoad() {
      // 异步更新数据
      // setTimeout 仅做示例，真实场景中一般为 ajax 请求
      this.loading = true
      this.query.page = this.query.page + 1
      console.log('onLoad')
      this.getDoctorsList(this.query)
    },
    onRefresh() {
      // 清空列表数据
      this.finished = false
      console.log('onRefresh')
      console.log(this.query)
      // 重新加载数据
      // 将 loading 设置为 true，表示处于加载状态
      this.loading = true
      this.query.page = 1
      this.list = []
      this.getDoctorsList(this.query)
    },
    async getDoctorsList(query) {
      try {
        let q = Object.assign({}, query, query)
        if (q.begin_time) {
          q.begin_time = dayjs(query.begin_time).format('YYYY-MM-DD') 
        }
        if (q.end_time) {
          q.end_time = dayjs(query.end_time).format('YYYY-MM-DD') 
        }
        await pharmacyInteractor.fetchPharmacyDoctorList(q).then(data => {
          if (this.query.page === 1) {
            this.list = data.doctors
          } else {
            this.list = [...this.list, ...data.doctors]
          }
          if (data.total === this.list.length) {
            this.finished = true
          } else {
            this.finished = false
          }
          console.log(this.list)
          this.loading = false
        })
      } catch (error) {
        console.log(error)
      }
    },
    async handleCounts(query) {
      try {
        let q = Object.assign({}, query, query)
        const test = await pharmacyInteractor.fetchPharmacyInfo(q)
        const data = Object.assign({}, test)
        this.counts = data
      } catch (error) {
        console.log(error)
      }
    },
  }
}
</script>

<style lang="scss" scoped>
::v-deep .van-dropdown-item--down {
  margin: 0px 6% !important;
}
.cell {
  background-color: white;
  margin: 10px;
  height: 88px;
  border-radius: 10px;
  display: flex;
}
.mid-card {
  margin: 0px -6% 0px 6%;
  width: 88%;
  background-color: white;
  border-radius: 5px;
}
.van-cell {
  background-color: #fcfcfc;
  margin: 0px 2% 0px 2%;
  border-radius: 5;
  width: 96%;
}
.cell-value {
  color: black;
  font-weight: bold;
}
</style>
