<template>
  <div>
   <van-tabs v-model="query.status" @click="onClick">
     <van-tab title="收益" name="1"></van-tab>
     <van-tab title="支出" name="2"></van-tab>
     <van-tab title="待入账" name="3"></van-tab>
   </van-tabs>
   <van-dropdown-menu active-color="#F7931C" >
     <van-dropdown-item :title='title' v-model="query.type" :options="option" @change="dropdownItemChange"/>
   </van-dropdown-menu>
   <van-cell>
	   <span style="font-size: 12px;float: right;">总{{status_name}}：{{total_amount}}</span>
   </van-cell>
    <van-pull-refresh v-model="loading" @refresh="onRefresh">
      <van-list
        v-model="loading"
        :finished="finished"
        :finished-text="list.length == 0 ? '': '没有更多了'"
        offset="150"
        @load="onLoad"
      >
        <div v-for="(item, index) in list" :key="index" class="cell" >
          <van-cell-group>
            <van-cell v-if="item.monthItem" style="background-color: #f3f3f3;">
            </van-cell>
            <van-cell v-else :title="item.commodity_name" :label="item.created_at">
              <template #default>
                <div :style="query.status | statusFilter">{{item.amount}}</div>
                <div :style="query.status | IncomestatusFilter">{{item.notice}}</div>
              </template>
            </van-cell>
          </van-cell-group>
        </div>
      </van-list>
    </van-pull-refresh>
    <van-empty v-if="list.length === 0 && loading === false " description="还没有数据哦">
      <van-button round plain class="bottom-button" size="small" @click="reFetchData">点击重试</van-button>
    </van-empty>
  </div>
</template>

<script>
	import {
		partnerInteractor
	} from '@/core'
	import store from '@/store'
	export default {
		name: 'month_list',
		props: {},
		data() {
			return {
				list: [],
				loading: false,
				finished: false,
				refreshing: false,
				status_name:'收益',
				total_amount: 0,
        option: [],
        title:'全部',
				query: {
					page: 0,
					size: 10,
					status:this.$route.query.status,
					year:this.$route.query.year,
					month:this.$route.query.month,
          type: 0
				}
			}
		},
		filters: {
		  statusFilter(status) {
		    const statusMap = {
		      1: 'color: orange;',
		      2: 'color: red;',
		      3: 'color: blue;',
		    }
		    return statusMap[status]
		  },
      IncomestatusFilter(status) {
        const IncomestatusMap = {
          1: 'color: red;',
          2: 'color: orange;',
          3: 'color: red;',
        }
        return IncomestatusMap[status]
      },
		},

		created() {

    },
		mounted() {

		},
		methods: {
      dropdownItemChange(value,text) {
        this.title = text;
        this.finished = false;
        this.loading = true;
        this.query.page = 1;
        this.list = [];
        this.fetchInfo(this.query)
      },
			onClick(name, title) {
			   this.finished = false
			   this.loading = true
			   this.query.page = 1
			   this.query.status = name;
         this.query.type = 0;
			   this.status_name = title;
			   this.list = []
			   this.fetchInfo(this.query)
			},
			reFetchData() {
			  this.finished = false
			  this.loading = true
			  this.query.page = 1
			  this.list = []
			  this.fetchInfo(this.query)
			},
			fetchInfo (params) {
			  partnerInteractor.fetchPromoterPartnersIncomeDetail(params).then(data => {
				  this.total_amount = data.total_amount;
          this.option = [];
			    if (this.query.page === 1) {
			    	this.list = data.list
			    } else {
			    	this.list = [...this.list, ...data.list]
			    }
          var type_list = data.type_list[this.query.status-1]['list'];
          type_list.forEach(element => {
            let item = {
              text: element.type_name,
              value: element.type_id
            }
            this.option.push(item)
          });
			    if (data.total <= this.list.length) {
			    	this.finished = true
			    }
			    this.loading = false
			  })
			},
			onLoad() {
				this.loading = true
				this.query.page = this.query.page + 1
				this.fetchInfo(this.query)
			},
			onRefresh() {
				// 清空列表数据
				this.finished = false
				this.loading = true
				this.query.page = 1
				this.list = []
				this.monthList = []
				this.fetchInfo(this.query)
			},

		}
	}
</script>

<style lang="scss" scoped>
</style>
