<template>
  <div class="edit-promoter-info">
    <!-- 页面标题 -->
    <van-nav-bar
      title="完善资料"
      left-text="返回"
      left-arrow
      @click-left="onClickLeft"
    />

    <!-- 表单内容 -->
    <van-form @submit="onSubmit">
      <van-cell-group>
        <!-- 卡户名 -->
        <van-field
          v-model="formData.accountName"
          name="accountName"
          label="卡户名"
          placeholder="请输入卡户名"
          input-align="right"
          :rules="[{ required: true, message: '请填写卡户名' }]"
        />

        <!-- 银行名称 -->
        <van-field
          v-model="formData.bankName"
          name="bankName"
          label="银行名称"
          placeholder="请输入银行名称"
          input-align="right"
          :rules="[{ required: true, message: '请填写银行名称' }]"
        />

        <!-- 卡号 -->
        <van-field
          v-model="formData.cardNumber"
          name="cardNumber"
          label="卡号"
          type="number"
          placeholder="请输入银行卡号"
          input-align="right"
          :rules="[{ required: true, message: '请填写银行卡号' }]"
        />

        <!-- 银行所在地 -->
        <van-field
          v-model="formData.bankLocationText"
          name="bankLocation"
          label="银行所在地"
          placeholder="请选择银行所在地"
          input-align="right"
          readonly
          is-link
          @click="showBankLocationPicker = true"
          :rules="[{ required: true, message: '请选择银行所在地' }]"
        />

        <!-- 开户银行预留手机号 -->
        <van-field
          v-model="formData.reservedPhone"
          name="reservedPhone"
          label="预留手机号"
          type="tel"
          placeholder="请输入开户银行预留手机号"
          input-align="right"
          :rules="[
            { required: true, message: '请填写预留手机号' },
            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
          ]"
        />
      </van-cell-group>

      <!-- 身份证上传区域 -->
      <van-cell-group class="upload-section">
        <van-cell>
          <template #title>
            <span class="upload-title">身份证正面 <span class="required">*</span></span>
          </template>
        </van-cell>
        <van-cell class="upload-cell">
          <van-uploader
            v-model="formData.idCardFront"
            :max-count="1"
            :preview-size="80"
            accept="image/*"
            :after-read="onReadIdCardFront"
            @delete="onDeleteIdCardFront"
          >
            <div class="upload-placeholder" v-if="!formData.idCardFront.length">
              <van-icon name="photograph" size="24" />
              <div class="upload-text">上传身份证正面</div>
            </div>
          </van-uploader>
        </van-cell>

        <van-cell>
          <template #title>
            <span class="upload-title">身份证反面 <span class="required">*</span></span>
          </template>
        </van-cell>
        <van-cell class="upload-cell">
          <van-uploader
            v-model="formData.idCardBack"
            :max-count="1"
            :preview-size="80"
            accept="image/*"
            @after-read="onReadIdCardBack"
            @delete="onDeleteIdCardBack"
          >
            <div class="upload-placeholder" v-if="!formData.idCardBack.length">
              <van-icon name="photograph" size="24" />
              <div class="upload-text">上传身份证反面</div>
            </div>
          </van-uploader>
        </van-cell>
      </van-cell-group>

      <!-- 银行所在地选择弹窗 -->
      <van-popup v-model="showBankLocationPicker" round position="bottom">
        <van-cascader
          v-model="bankLocationValues"
          title=""
          active-color="#f39800"
          :options="areasList"
          @close="showBankLocationPicker = false"
          @finish="onBankLocationFinish"
          :field-names="fieldNames"
        >
          <template #title>
            <div style="display: flex;flex-direction: row;justify-content: space-around;padding: 10px; align-items: center;">
              <div>请选择银行所在地</div>
              <van-button
                round
                color="#f39800"
                plain
                style="width: 100px; margin-left: 10px;"
                size="small"
                @click="resetBankLocation"
              >
                重置
              </van-button>
            </div>
          </template>
        </van-cascader>
      </van-popup>

      <!-- 提交按钮 -->
      <div class="submit-section">
        <van-button
          round
          block
          type="primary"
          native-type="submit"
          color="linear-gradient(to right, #F7941E, #F24316)"
          class="submit-button"
        >
          保存资料
        </van-button>
      </div>
    </van-form>
  </div>
</template>

<script>
import { Toast } from 'vant'
import { indexInteractor } from '@/core'

export default {
  name: 'editPromoterInfo',
  props: {},
  data() {
    return {
      formData: {
        accountName: '',
        bankName: '',
        cardNumber: '',
        bankLocationText: '',
        bankLocationValue: '',
        reservedPhone: '',
        idCardFront: [],
        idCardBack: []
      },
      // 地区选择相关
      showBankLocationPicker: false,
      bankLocationValues: undefined,
      areasList: [],
      fieldNames: {
        text: 'name',
        value: 'id',
        children: 'children',
      }
    }
  },

  created() {
    this.fetchAreaList()
  },
  mounted() {

  },
  methods: {
    // 返回按钮点击事件
    onClickLeft() {
      this.$router.go(-1)
    },

    // 表单提交
    onSubmit(values) {
      // 验证身份证是否已上传
      if (!this.formData.idCardFront.length) {
        Toast('请上传身份证正面')
        return
      }
      if (!this.formData.idCardBack.length) {
        Toast('请上传身份证反面')
        return
      }

      Toast.success('资料保存成功')
      console.log('表单数据:', values)
      console.log('完整数据:', this.formData)
    },

    // 身份证正面上传
    onReadIdCardFront(file) {
      console.log('身份证正面上传:', file)
      // 这里可以添加图片压缩、格式验证等逻辑
      var formData = new FormData();
      formData.append("file", file.file);
      indexInteractor.fetchCommunityFileUpload(formData).then(result => {
          const url = result.file.url; // Get url from response
          this.fetchBankCardSave(result.file.id)
        })
        .catch(err => {
          console.log(err);
        });
    },
    fetchBankCardSave(id) {
      indexInteractor.fetchBankCardSave({identity_front_file_id : id}).then(data => {
        console.log(data)
      })
    },
    // 删除身份证正面
    onDeleteIdCardFront() {
      console.log('删除身份证正面')
    },

    // 身份证反面上传
    onReadIdCardBack(file) {
      console.log('身份证反面上传:', file)
      // 这里可以添加图片压缩、格式验证等逻辑
      var formData = new FormData();
      formData.append("file", file.file);
      indexInteractor.fetchCommunityFileUpload(formData).then(result => {
          const url = result.file.url; // Get url from response
          this.fetchBankCardSave(result.file.id)
        })
        .catch(err => {
          console.log(err);
        });
    },

    // 删除身份证反面
    onDeleteIdCardBack() {
      console.log('删除身份证反面')
    },

    // 获取地区列表
    fetchAreaList() {
      indexInteractor.fetchGetAreas({ level: 2 }).then(data => {
        console.log('获取到的地区数据:', data)
        this.areasList = data
      }).catch(err => {
        console.error('获取地区数据失败:', err)
        Toast.fail('获取地区数据失败')
      })
    },

    // 银行所在地选择完成
    onBankLocationFinish() {
      // 获取所有参数
      const args = Array.from(arguments)
      console.log('级联选择器回调参数:', args)

      this.showBankLocationPicker = false

      // 根据参数结构处理数据
      let value, selectedOptions

      if (args.length >= 2) {
        // 标准参数结构：(value, selectedOptions)
        value = args[0]
        selectedOptions = args[1]
      } else if (args.length === 1 && typeof args[0] === 'object') {
        // 对象参数结构：({value, selectedOptions})
        value = args[0].value
        selectedOptions = args[0].selectedOptions
      }

      console.log('处理后的 value:', value)
      console.log('处理后的 selectedOptions:', selectedOptions)

      // 设置显示文本和值
      if (selectedOptions && selectedOptions.length > 0) {
        // 拼接省市名称显示
        const locationText = selectedOptions.map(option => option.name).join(' ')

        // 直接赋值
        this.formData.bankLocationText = locationText

        // 根据 value 的类型设置值
        if (typeof value === 'object' && value !== null && 'value' in value) {
          this.formData.bankLocationValue = value.value
        } else {
          this.formData.bankLocationValue = value
        }

        console.log('设置后的表单数据:', JSON.stringify(this.formData))

        // 更新 cascader 的 v-model 值
        this.bankLocationValues = value

        // 强制更新视图
        this.$forceUpdate()
      } else {
        console.error('无法获取选中的地区信息')
        Toast('地区选择失败，请重试')
      }
    },

    // 重置银行所在地选择
    resetBankLocation() {
      this.showBankLocationPicker = false
      this.bankLocationValues = undefined
      this.formData.bankLocationText = ''
      this.formData.bankLocationValue = ''
    }
  }
}
</script>

<style lang="scss" scoped>
.edit-promoter-info {
  min-height: 100vh;
  background-color: #f7f8fa;

  .van-nav-bar {
    background-color: #fff;
  }

  .van-cell-group {
    margin-top: 12px;

    &.upload-section {
      .van-cell {
        padding: 12px 16px;
      }
    }
  }

  .upload-title {
    font-size: 14px;
    color: #323233;

    .required {
      color: #ee0a24;
      margin-left: 2px;
    }
  }

  .upload-cell {
    padding: 8px 16px !important;

    .van-uploader {
      width: 100%;
    }
  }

  .upload-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background-color: #f7f8fa;
    border: 1px dashed #dcdee0;
    border-radius: 4px;
    color: #969799;

    .upload-text {
      font-size: 12px;
      margin-top: 4px;
      text-align: center;
      line-height: 1.2;
    }
  }

  .submit-section {
    padding: 24px 16px;
    background-color: #fff;
    margin-top: 12px;

    .submit-button {
      height: 44px;
      font-size: 16px;
      font-weight: 500;
    }
  }

  // 表单字段样式调整
  .van-field {
    &__label {
      color: #323233;
      font-weight: 500;
    }

    &__control {
      color: #323233;
    }
  }

  // 上传组件样式调整
  .van-uploader {
    &__preview {
      margin-right: 8px;
    }

    &__upload {
      position: relative;
    }
  }
}
</style>
