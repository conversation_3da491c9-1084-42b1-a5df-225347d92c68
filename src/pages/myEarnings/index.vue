<template>
  <div>
    <div v-if="account" class="note" :style ="note">
      <div style="height: 30px; display: flex; justify-content: space-between;padding: 20px 10px 0 10px;align-items: center;" >

        <div style="height:30px;color: white;font-weight: bold;line-height: 30px;">可兑换喜分</div>
        <van-button round plain class="bottom-button" size="small" to="/editPromoterInfo">完善资料</van-button>
      </div>
      <div style="justify-content: space-between;display: flex;align-items: center;">
        <div v-if="account && account.available" style="font-size: 24px;margin: 10px;font-weight: bold;color: white;">{{account.available}}</div>
        <div v-if="settlement !== undefined">
          <div v-if="settlement === false" style="text-align: right;margin-right: 10px;font-weight: bold;color:white;">
            <div style="font-size:14px">如需提现请联系客服</div>
            <a style="font-size:18px; color:white;text-decoration:underline;" href="tel:**********">**********</a>
          </div>
          <van-button v-else style="margin-right:20px" plain hairline type="info" color="white" size="small" to="/monthlySeted">已开通月结兑换服务></van-button>
        </div>

      </div>
      <van-divider style="margin: 0px;" dashed/>
      <div style="display: flex;">
        <!-- <div style="text-align: center;margin: 10px;">
          <div>{{userInfo.account.unaccount}}</div>
          <div style="color: gray;font-size: 14px;">未到账</div>
        </div> -->
        <div style="text-align: center;margin: 10px;">
          <div v-if="account && account.frozen" style="color: white;font-size: 14px;">正在兑换中喜分 {{account.frozen}}</div>
        </div>
      </div>
    </div>
    <div style="height: 6px;background-color: #f3f3f3;"></div>

    <van-pull-refresh v-model="loading" @refresh="onRefresh">
      <van-list
        v-model="loading"
        :finished="finished"
        :finished-text="list.length == 0 ? '': '没有更多了'"
        offset="150"
        @load="onLoad"
      >
        <div v-for="(item, index) in list" :key="index" class="cell">
        	<van-cell-group>
        		<div style="height:40px;text-align:left;line-height: 40px;margin-left: 10px;font-size: 15px;">
        			{{item.year}}年{{item.month}}月
        			<span style="float: right;margin-right: 20px;color:blue;font-size: 15px;" @click="jumpToIncomeDetailList(item)">明细</span>
        		</div>
        	</van-cell-group>
        	<van-cell-group>
        		<van-cell>
        			<template>
        				<div>
        					<van-row justify="space-around" class="amount">
        						<van-col span="6" offset="2" >{{item.income}}</van-col>
        						<van-col span="6" offset="2" >{{item.pay}}</van-col>
        						<van-col span="6" offset="2" >{{item.waiting_income}}</van-col>
        					</van-row>
        					<van-row justify="space-around">
        						<van-col span="6" offset="2">收益</van-col>
        						<van-col span="6" offset="2">支取</van-col>
        						<van-col span="6" offset="2">待入账</van-col>
        					</van-row>
        				</div>
        			</template>
        		</van-cell>
        	</van-cell-group>
        </div>
      </van-list>
    </van-pull-refresh>
    <van-empty v-if="list.length === 0 && loading === false " description="还没有数据哦">
      <van-button round plain class="bottom-button" size="small" @click="reFetchData">点击重试</van-button>
    </van-empty>

    <div class="my-earnings">
      <!-- <div class="header">
        <h2>我的收益</h2>
      </div> -->
      
      <!-- 登录状态检查 -->
      <div v-if="!isLoggedIn" class="login-section">
        <!-- <div class="login-prompt">
          <p>请先登录查看您的收益信息</p>
        </div>
        
        <div class="wechat-login">
          <van-button 
            class="wechat-btn" 
            round 
            color="#07C160" 
            icon="wechat" 
            @click="handleWechatLogin"
            :loading="wechatLoading"
          >
            微信登录
          </van-button>
        </div>
        <div class="other-login">
          <van-button 
            class="normal-login-btn" 
            round 
            plain 
            color="#F7941E" 
            @click="goToLogin"
          >
            账号密码登录
          </van-button>
        </div> -->
      </div>
      
      <!-- 已登录状态显示收益信息 -->
      <div v-else class="earnings-content">
        <div class="earnings-card">
          <div class="earnings-item">
            <div class="earnings-label">今日收益</div>
            <div class="earnings-value">¥{{ todayEarnings }}</div>
          </div>
          <div class="earnings-item">
            <div class="earnings-label">本月收益</div>
            <div class="earnings-value">¥{{ monthEarnings }}</div>
          </div>
          <div class="earnings-item">
            <div class="earnings-label">总收益</div>
            <div class="earnings-value">¥{{ totalEarnings }}</div>
          </div>
        </div>
        
        <div class="user-info">
          <van-cell-group>
            <van-cell title="用户名" :value="userInfo.name" />
            <van-cell title="手机号" :value="userInfo.phone" />
          </van-cell-group>
        </div>
        
        <div class="logout-section">
          <van-button 
            class="logout-btn" 
            round 
            plain 
            color="#ee0a24" 
            @click="handleLogout"
          >
            退出登录
          </van-button>
        </div>
      </div>
    </div>
  </div>
  
</template>

<script>
import { loginInteractor, indexInteractor, partnerInteractor } from '@/core'
import { getToken, setToken, removeToken, getFromLocalStorage, saveToLocalStorage, removeFromLocalStorage } from '@/core/services/cache'
import { Toast, Dialog } from 'vant'
import store from '@/store'
import dayjs from 'dayjs'
import quarterOfYear from 'dayjs/plugin/quarterOfYear' // ES 2015
import 'dayjs/locale/zh-cn' // ES 2015

dayjs.locale('zh-cn')
dayjs.extend(quarterOfYear)

export default {
  name: 'MyEarnings',
  props: {},
  data() {
    return {
        account: undefined,
        searchValue: '',
        monthList: [],
        list: [],
        loading: false,
        finished: false,
        refreshing: false,
        actionShow: false,
        incomeType: '全部来源',
        actions: [{ name: '全部来源', key: undefined }],
        query: {
          page: 0,
          size: 10,
          source_name: ''
        },
        settlement: undefined,
        note: {
                    backgroundImage: "url(" + require("../../assets/month_list_bg.png") + ")",
                    width: "100%",
                    backgroundSize:'cover',
                  },
      isLoggedIn: false,
      wechatLoading: false,
      wxuserInfo: {
        name: '',
        phone: ''
      },
      todayEarnings: '0.00',
      monthEarnings: '0.00',
      totalEarnings: '0.00'
    }
  },
  created() {
    this.checkLoginStatus()
  },
  mounted() {
    // 检查是否从微信授权回调返回
    this.handleWechatCallback()
  },
  methods: {
    jumpToIncomeDetailList(item){
	    this.$router.push({ path: '/incomeDetailList', query:{status: 1,year:item.year,month:item.month}})
	  },
    reFetchData() {
      this.finished = false
      this.loading = true
      this.query.page = 1
      this.list = []
      this.monthList = []
      this.query['month'] = undefined
      this.fetchDoctorIncomeListV2(this.query)
    },
    onActionSelect(item) {
      this.actionShow = false
      this.incomeType = item.name
      console.log(item)
      this.finished = false
      this.loading = true
      this.query.page = 1
      this.list = []
      this.monthList = []
      if (item.key) {
        this.query['commodity_type'] = item.key
      } else {
        this.query['commodity_type'] = undefined
      }
      this.fetchDoctorIncomeListV2(this.query)
    },
    onSearch(i) {
      console.log(i)
      this.query.source_name = i
      this.finished = false
      this.loading = true
      this.query.page = 1
      this.list = []
      this.monthList = []
      this.fetchDoctorIncomeListV2(this.query)
    },
    onLoad() {
      this.loading = true
      this.query.page = this.query.page + 1
      this.fetchDoctorIncomeListV2(this.query)
    },
    onRefresh() {
      // 清空列表数据
      this.finished = false
      this.loading = true
      this.query.page = 1
      this.list = []
      this.monthList = []
      this.fetchDoctorIncomeListV2(this.query)
    },
    async fetchDoctorIncomeListV2(query) {
      try {
        await partnerInteractor.fetchPromoterPartnersAccountInfo(query).then(data => {
          this.account = data.account
          if (this.query.page === 1) {
            this.list = data.list
          } else {
            this.list = [...this.list, ...data.list]
          }
          if (data.total <= this.list.length) {
            this.finished = true
          }
          this.loading = false
        })
      } catch (error) {
        console.log(error)
      }
    },
    // 检查登录状态
    checkLoginStatus() {
      const token = getToken()
      const userInfo = getFromLocalStorage('info')
      
      if (token && userInfo) {
        this.isLoggedIn = true
        this.userInfo = {
          name: userInfo.name || '未知用户',
          phone: userInfo.phone || '未绑定'
        }
        this.loadEarningsData()
      }
    },
    
    // 微信登录
    async handleWechatLogin() {
      try {
        this.wechatLoading = true
        
        // 检查是否在微信环境中
        if (this.isWechatBrowser()) {
          // 在微信浏览器中，直接跳转到微信授权
          this.redirectToWechatAuth()
        } else {
          // 不在微信环境中，显示提示
          Dialog.alert({
            title: '提示',
            message: '请在微信中打开此页面进行微信登录'
          })
        }
      } catch (error) {
        console.error('微信登录失败:', error)
        Toast.fail('微信登录失败，请重试')
      } finally {
        this.wechatLoading = false
      }
    },
    
    // 检查是否在微信浏览器中
    isWechatBrowser() {
      const ua = navigator.userAgent.toLowerCase()
      return ua.includes('micromessenger')
    },
    
    // 跳转到微信授权页面
    redirectToWechatAuth() {
      // 微信授权参数
      const appId = process.env.VUE_APP_WECHAT_APPID || 'your_wechat_appid'
      const redirectUri = encodeURIComponent(window.location.origin + '/myEarnings')
      const scope = 'snsapi_userinfo'
      const state = 'wechat_login_' + Date.now()
      
      // 保存state用于验证
      sessionStorage.setItem('wechat_login_state', state)
      
      // 构建微信授权URL
      const authUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appId}&redirect_uri=${redirectUri}&response_type=code&scope=${scope}&state=${state}#wechat_redirect`
      
      // 跳转到微信授权页面
      window.location.href = authUrl
    },
    
    // 处理微信授权回调
    async handleWechatCallback() {
      const urlParams = new URLSearchParams(window.location.search)
      const code = urlParams.get('code')
      const state = urlParams.get('state')
      
      if (code && state) {
        // 验证state
        const savedState = sessionStorage.getItem('wechat_login_state')
        if (state !== savedState) {
          Toast.fail('授权验证失败')
          return
        }
        
        try {
          // 调用后端API进行微信登录
          await this.wechatLoginWithCode(code)
          
          // 清理URL参数
          window.history.replaceState({}, document.title, window.location.pathname)
          sessionStorage.removeItem('wechat_login_state')
        } catch (error) {
          console.error('微信登录处理失败:', error)
          Toast.fail('微信登录失败')
        }
      }
    },
    
    // 使用微信授权码登录
    async wechatLoginWithCode(code) {
      try {
        // 调用后端的微信登录接口
        const response = await loginInteractor.wechatLogin({ code })
        
        if (response.token) {
          setToken(response.token)
          
          // 获取用户信息
          const userInfo = await indexInteractor.getPromoterInfo()
          saveToLocalStorage('info', userInfo)
          
          this.isLoggedIn = true
          this.userInfo = {
            name: userInfo.name || '微信用户',
            phone: userInfo.phone || '未绑定'
          }
          
          Toast.success('微信登录成功')
          this.loadEarningsData()
        }
      } catch (error) {
        console.error('微信登录失败:', error)
        Toast.fail('微信登录失败，请重试')
        throw error
      }
    },
    
    // 跳转到普通登录页面
    goToLogin() {
      this.$router.push('/login')
    },
    
    // 加载收益数据
    async loadEarningsData() {
      try {
        // 这里应该调用实际的收益数据API
        // 目前使用模拟数据
        this.todayEarnings = '128.50'
        this.monthEarnings = '3,256.80'
        this.totalEarnings = '15,678.90'
      } catch (error) {
        console.error('加载收益数据失败:', error)
      }
    },
    
    // 退出登录
    async handleLogout() {
      try {
        await Dialog.confirm({
          title: '确认退出',
          message: '确定要退出登录吗？'
        })
        
        removeToken()
        removeFromLocalStorage('info')
        
        this.isLoggedIn = false
        this.userInfo = { name: '', phone: '' }
        this.todayEarnings = '0.00'
        this.monthEarnings = '0.00'
        this.totalEarnings = '0.00'
        
        Toast.success('已退出登录')
      } catch (error) {
        // 用户取消退出
      }
    }
  }
}
</script>

<style lang="scss" scoped>

.amount {
  font-weight: bold;
  color: orange;
}
.my-earnings {
  min-height: 100vh;
  background-color: #f5f5f5;
  
  .header {
    background: linear-gradient(to right, #F7941E, #F24316);
    color: white;
    text-align: center;
    padding: 20px 0;
    
    h2 {
      margin: 0;
      font-size: 18px;
      font-weight: 500;
    }
  }
  
  .login-section {
    padding: 40px 20px;
    text-align: center;
    
    .login-prompt {
      margin-bottom: 40px;
      
      p {
        color: #666;
        font-size: 16px;
        margin: 0;
      }
    }
    
    .wechat-login {
      margin-bottom: 20px;
      
      .wechat-btn {
        width: 280px;
        height: 44px;
        font-size: 16px;
        
        ::v-deep .van-icon {
          font-size: 20px;
          margin-right: 8px;
        }
      }
    }
    
    .other-login {
      .normal-login-btn {
        width: 280px;
        height: 44px;
        font-size: 16px;
      }
    }
  }
  
  .earnings-content {
    padding: 20px;
    
    .earnings-card {
      background: white;
      border-radius: 12px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      
      .earnings-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 0;
        border-bottom: 1px solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }
        
        .earnings-label {
          color: #666;
          font-size: 14px;
        }
        
        .earnings-value {
          color: #F7941E;
          font-size: 18px;
          font-weight: 600;
        }
      }
    }
    
    .user-info {
      margin-bottom: 30px;
      
      ::v-deep .van-cell {
        font-size: 14px;
      }
    }
    
    .logout-section {
      text-align: center;
      
      .logout-btn {
        width: 200px;
        height: 40px;
      }
    }
  }
}
</style>
