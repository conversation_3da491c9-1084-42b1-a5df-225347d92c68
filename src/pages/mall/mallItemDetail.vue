<template>
  <div class="bg">
    <van-swipe class="my-swipe" :autoplay="3000" indicator-color="white" v-if="goodsInfo && (slideList.length != 0)">
      <van-swipe-item v-for="(item, index) in slideList" :key="index">
        <van-image :src="item.url" />
      </van-swipe-item>
    </van-swipe>
    <van-swipe class="my-swipe" :autoplay="3000" indicator-color="white" v-else>
      <van-swipe-item v-if="goodsInfo && goodsInfo.cover_image_file">
        <van-image  :src="goodsInfo.cover_image_file.url" />
      </van-swipe-item>
    </van-swipe>
    <van-cell-group>
      <van-cell v-if="goodsInfo" :title="goodsInfo.name" :value="goodsInfoPrice()" :label="goodsInfo.title" value-class="value-class"/>
      <van-cell title="快递费" value="包邮"/>
      <!-- <van-cell title="兑换规则" value="查看详情" /> -->
      <van-cell title="商品详情" />
      <div v-if="goodsInfo" class="des" v-html="goodsInfo.description" style="margin: 6px;"></div>
    </van-cell-group>
    <div style="height:60px;"></div>
    <div class="footer" style="height: 60px; text-align: center;padding: 10px 0;background-color: transparent;">
      <van-button round :disabled="goodsInfo.promoter_integral > info.point" class="payButton" @click="payButtonAction" >立即兑换</van-button>
    </div>
  </div>
</template>

<script>
import { indexInteractor, mallInteractor } from '@/core'
import store from '@/store'
import { Dialog, Toast } from 'vant';
import { readFromLocalStorage } from '@/core/services/cache'
export default {
  name: 'MallItemDetail',
  data () {
    return {
      info: undefined,
      slideList: [],
      list: [],
      goodsInfo: undefined
    }
  },

  methods: {
    async fetchInfo() {
      try {
        const info = await indexInteractor.getPromoterInfo()
        const data = Object.assign({}, info)
        this.info = data
        saveToLocalStorage('info', data)
      } catch (error) {
        console.log(error)
      }
    },
    fetchGoodsInfo () {
      var id = this.$route.params.id
      var params = {id: id}
      var _this = this
      mallInteractor.fetchMallGoodsInfo(params).then(data => {
        _this.goodsInfo = data.goodsinfo
        _this.slideList = data.goodsinfo.albums
      })
    },
    goodsInfoPrice() {
      return this.goodsInfo.promoter_integral + '积分'
    },
    payButtonAction() {
      this.fetchBuyGoods()
    },
    fetchBuyGoods () {
      var id = this.$route.params.id
      var params = {id: id, number: 1}
      mallInteractor.fetchMallBuyGoods(params).then(data => {
        this.$router.push({ path: '/mallItemEdit', query:{id: this.$route.params.id, type: this.goodsInfo.type}})
      })
    },
  },
  computed: {

  },
  mounted () {
    this.fetchGoodsInfo()
  },
  created () {
    this.fetchInfo()
  },

}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.orange-button {
  background-color: #FF9000  !important;
  color: white !important;
  border: 1px solid #FF9000 !important;
}
.ssss >>>.prize-list{
  color: black
}
.payButton {
  background-color: #F7941E;
  border: 1px solid #F7941E;
  color: white;
  width: 200px;
  background-color: #F7941E;
  border: 1px solid #F7941E;
  width: 300px;
}
.footer{
  height: 100px;
  position: fixed;
  z-index: 10;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  color: white;
  background-color: #E6454A;
}

.value-class {
  color: #F7941E !important;
}
.des /deep/ img{
  width: 100%;
}
</style>

