<template>
  <div class="bg">
    <div style="margin-top:10px" v-if="goodsInfoType != 2">
      <van-panel title="" use-header-slot>
        <div slot="header" class="address-header">
          <div style="font-size: 14px; color: #9B9B9B">收货地址</div>
        </div>
        <van-radio-group v-model="address">
          <van-cell-group>
            <van-cell
              v-for="item in addresses"
              :key="item.id"
              :title="item.region + item.detail + ' ' + item.name + ' ' + item.mobile"
              clickable
              @click="addressOnChange(item)"
            >
              <van-radio
                slot="right-icon"
                checked-color="#ff9000"
                :name="item.id"
              />
            </van-cell>
          </van-cell-group>
        </van-radio-group>
      </van-panel>
      <div style="line-height: 44px;text-align: center;background-color: white;"><van-button style="border: 0;" plain size="small" color="#ff9000" to="/mallAddressEdit?type=2">+ 添加地址</van-button></div>
    </div>
    <van-panel title="商品详情" style="margin-top:10px" v-if="goodsInfo">
      <van-cell-group>
        <van-card
          num="1"
          :desc="goodsInfo.title"
          :title="goodsInfo.name"
          currency=""
          :thumb="goodsInfo.cover_image_file.url"
          style="background-color: white"
        >
          <template #price>
            <div>{{goodsInfoPrice()}}</div>
          </template>
        </van-card>
        <van-field name="quantity" label="数量" input-align="right">
          <template #input>
            <van-stepper :value="quantity" integer disable-input  min="1" max="10" async-change @change="stepperOnChange"/>
          </template>
        </van-field>
        <van-cell title="快递费" value="包邮" />
      </van-cell-group>
    </van-panel>

    <van-submit-bar label="总计 " text-align="left" button-text="立即兑换" @submit="payAction" >
      <div style="width: 260px">
        <div>总计</div>
        <div style="color:#f99000;font-weight:bold;font-size:16px">{{totalAmount}}</div>
      </div>
    </van-submit-bar>
  </div>
  
</template>

<script>
import { mallInteractor } from '@/core'
import store from '@/store'
import { Dialog, Toast } from 'vant';

export default {
  name: 'MallItemEdit',
  data () {
    return {
      address: undefined,
      addresses: [],
      quantity:1,
      goodsInfoType: 2,
      goodsInfo: undefined,
      totalAmount: undefined,
      usableCouponsList: [],
      selectCoupon: undefined,
      popupShow: false,
      couponCheckbox: undefined
    }
  },

  methods: {
    radioChange(item) {
      console.log(item)
      this.usableCouponsList.forEach(element => {
        if (element.id === item) {
          this.selectCoupon = element
          this.totalAmount = this.goodsInfoPriceQuantity()
        }
      });
    },
    couponClick() {
      if (this.usableCouponsList.length > 0) {
        this.popupShow = true
      }
    },
    fetchGoodsInfo () {
      var id = this.$route.query.id
      var params = {id: id}
      mallInteractor.fetchMallGoodsInfo(params).then(data => {
        this.goodsInfo = data.goodsinfo
        this.totalAmount = this.goodsInfoPriceQuantity()
      })
    },
    fetchAddressList () {
      mallInteractor.fetchMallAddressList().then(data => {
        this.addresses = data.address
      }).catch((err) => {
        this.$message({
          message: err,
          type: 'error'
        })
        console.log(err, '错误')
      })
    },
    goodsInfoPrice() {
      return this.goodsInfo.promoter_integral + '积分'
    },
    stepperOnChange(value) {
      Toast.loading({ forbidClick: true });
      var id = this.$route.query.id
      var params = {id: id, number: value}
      mallInteractor.fetchMallBuyGoods(params).then((res) => {
        this.totalAmount = this.goodsInfoPriceQuantity()
        this.quantity = value
        this.fetchMallCheckCoupons()
      })
    },
    addressOnChange(item) {
      this.address = item.id
    },
    goodsInfoPriceQuantity() {
      Toast.clear()

      return this.goodsInfo.promoter_integral * this.quantity + '积分'
    },

    payAction() {
      var params
      var id = this.$route.query.id
      if (this.goodsInfoType == 1 ) {
        if (!this.address) {
          Toast('请选择收货地址')
          return
        }
        params = {address_id: this.address, goods_id: id}
      } else {
        params = {goods_id: id}
      }
      
      
      mallInteractor.fetchMallCreateOrder(params).then(data => {
        this.$router.replace({ path: '/mallItemBuySuccess', query: { totalAmount: this.totalAmount, img: this.goodsInfo.cover_image_file.url }})
      }).catch((err) => {
        Toast.fail(err.message)
        console.log(err)
      })
    }
  },
  computed: {

  },
  mounted () {
    this.fetchGoodsInfo()
    if (this.goodsInfoType == 1) {
      this.fetchAddressList()
    }
  },
  created () {
    this.goodsInfoType = this.$route.query.type
  },
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.orange-value {
  color: #ff9000;
}
.gray-value {
  color: #f3f3f3;
}
.bg {
  /* background-color: aqua; */
  height: 100%;
}
.header-title {
  color: #FF9000;
  font-weight: bold;
  margin-top: 10px;
  font-size: 16px;
}
.footer{
  height: 50px;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  color: white;
}
.orange-button {
  background-color: #FF9000  !important;
  color: white !important;
  border: 1px solid #FF9000 !important;
}
.ssss >>>.prize-list{
  color: black
}
.header {
  height: 40px;
  background: linear-gradient(45deg, #F24316, #F7941E);
  display: flex;
  justify-content: center;
  align-items: center;
}
.address-header{
  display: flex;
  height: 44px;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  background-color: white;
}
::v-deep .van-panel__header {
  background-color: #f3f3f3;
  color: #9B9B9B;
}
::v-deep .van-cell {
  padding: 6px 16px;
}
::v-deep .van-cell__value {
  -webkit-box-flex: 2;
  -webkit-flex:2;
  flex: 2;
  width: 98%;
}
.payButton {
  background-color: #F7941E;
  border: 1px solid #F7941E;
  color: white;
  width: 200px;
  background-color: #F7941E;
  border: 1px solid #F7941E;
  width: 300px;
}


::v-deep .van-submit-bar__bar {
  height: 60px;
  justify-content: space-between !important;
  -webkit-justify-content: space-between !important;
  -webkit-box-pack: space-between !important;
}
::v-deep .van-submit-bar__text {
  font-size: 22px;
  font-weight: bold;;
}
::v-deep .van-submit-bar__price {
  margin-left: 10px;
  font-size: 15px;
  font-weight: 400;
}
::v-deep .van-submit-bar__price .van-submit-bar__price--integer {
  margin-left: 0px;
  font-size: 25px;
  font-weight: 400;
}
::v-deep .van-submit-bar__price--integer {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  font-weight: 400;
}

::v-deep .van-radio__label {
  width: 100%;
}

</style>

