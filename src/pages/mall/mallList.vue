<template>
  <div style="background-color:white">
    <!-- <van-search
      v-model="query.keyword"
      show-action
      placeholder="请输入搜索关键词"
      @search="onSearch"
      @cancel="onCancel"
    /> -->
    <div v-if="info" style="display: flex; flex-direction: row;justify-content: space-evenly;height: 80px;">
      <div class="header-item" @click="jumpToPointsDetailList()">
        <div class="header-item-s">
          <van-icon name="points" />
          <div class="header-item-t">{{ info.point }}积分</div>
        </div>
        <div class="header-item-d">收支/订单</div>
      </div>
      <div style="width: 1px;background-color: gainsboro;margin: 20px 0; "></div>
      <div class="header-item" @click="itemAction()">
        <div class="header-item-s">
          <van-icon name="notes-o" />
          <div class="header-item-t">积分任务</div>
        </div>
        <div class="header-item-d">做任务赚积分</div>
      </div>
    </div>
    <van-dropdown-menu active-color="#F7931C" >
      <van-dropdown-item v-model="integral_sort" :options="option0" @change="dropdownItemChange"/>
      <van-dropdown-item v-model="integral" :options="option1" @change="dropdownItemChange"/>
      <van-dropdown-item v-model="category_id" :options="option2"  @change="dropdownItemChange"/>
    </van-dropdown-menu>
    <!-- <van-tabs v-if="query.score_type == 1" v-model="tabActive0" type="card" color="#ff9000" @click="tabOnClick0">
      <van-tab title="0-5k"/>
      <van-tab title="5k-10k"/>
      <van-tab title="10k-20k"/>
      <van-tab title="20k+"/>
    </van-tabs>
    <van-tabs v-if="query.score_type == 2" v-model="tabActive1" type="card" color="#ff9000" @click="tabOnClick1">
      <van-tab title="0-30"/>
      <van-tab title="30-80"/>
      <van-tab title="80-200"/>
      <van-tab title="200+"/>
    </van-tabs> -->
    <van-pull-refresh v-model="loading" @refresh="onRefresh" :disabled=true >
      <van-list
        v-model="loading"
        :finished="finished"
        finished-text="没有更多了"
        offset="150"
        @load="onLoad"
      >
          <van-cell v-for="(item, index) in list" :key="index" @click="pushDetail(item)"  :border="false">
            <template #title>
              <div style="display:flex">
                <div style="border: 1px solid #ff9000;border-radius: 5px;display: flex;">
                  <van-image style="padding:3px" fit="contain" width="100" height="100" :src="item.cover_image_file.url" />
                </div>
                
                <div style="display: flex;flex-direction: column;justify-content: space-between;margin-left: 5px;">
                  <p class="goods-name">{{item.name}}</p>
                  <div class="price">{{item.promoter_integral}}积分</div>
                </div>
              </div>
            </template>
            <!-- <template #default>
              <div style="display: flex;align-items: center;height: 100%;justify-content: flex-end;">
                <van-button color="#ff9000" round size="small" @click="pushDetail(item)">立即兑换</van-button>
              </div>
            </template> -->
          </van-cell>
      </van-list>
    </van-pull-refresh>
  </div>
</template>

<script>
import { mallInteractor, indexInteractor } from '@/core'
export default {
  name: 'MallIndexList',
  data () {
    return {
      info: undefined,
      tabActive0: 0,
      tabActive1: 0,
      list: [],
      loading: false,
      finished: false,
      refreshing: false,
      integral: 99,
      category_id: undefined,
      integral_sort: 0,
      option0: [
        { text: '默认排序', value: 0},
        { text: '积分从低到高', value: 1},
        { text: '积分从高到低', value: 2}
      ],
      integralList: [[0,1000], [1000, 5000], [5000, 20000], [20000, undefined] ],
      option1: [
        { text: '全部积分', value: 99},
        { text: '0-1k', value: 0},
        { text: '1k-5k', value: 1},
        { text: '5k-20k', value: 2},
        { text: '20k+', value: 3},
      ],
      option2: [],
      query: {
        page: 0,
        size: 10,
        max_integral: undefined,
        min_integral: undefined,
        integral_sort: 0
      },
      img_list: []
    }
  },
  methods: {
    async fetchInfo() {
      try {
        const info = await indexInteractor.getPromoterInfo()
        const data = Object.assign({}, info)
        this.info = data
        saveToLocalStorage('info', data)
      } catch (error) {
        console.log(error)
      }
    },
    itemAction() {
      this.$router.push({path:'/points'})
    },
    jumpToPointsDetailList() {
      this.$router.push({path:'/pointsDetailList'})
    },
    swipeClick(url){
      window.location.href = url
    },
    onSearch() {
      this.onRefresh()
    },
    onCancel() {
      this.onRefresh()
    },
    dropdownItemChange() {
      if (this.integral != undefined && this.integral != 99) {
        this.query.min_integral = this.integralList[this.integral][0]
        this.query.max_integral = this.integralList[this.integral][1]
        this.onRefresh()
      } else {
        this.query.min_integral = undefined
        this.query.max_integral = undefined
        this.onRefresh()
      }
      this.query.integral_sort = this.integral_sort
      this.query["category_id"] = this.category_id
    },
    onLoad () {
      this.loading = true
      this.query.page = this.query.page + 1
      this.fetchList(this.query)
    },
    onRefresh () {
      // 清空列表数据
      this.finished = false
      this.loading = true
      this.query.page = 1
      this.list = []
      this.fetchList(this.query)
    },
    //商品列表
    async fetchList (query) {
      mallInteractor.fetchMallStoreList(query).then(data => {
        if (this.query.page === 1) { 
          this.list = data.goods
        } else {
          this.list = [...this.list, ...data.goods]
        }
        if (data.total <= this.list.length) {
          this.finished = true
        }
        this.loading = false
      })
    },
    loadFetch() {
      this.onLoad()
      this.fetchMallStoreCategories()
    },
    fetchMallStoreCategories () {
      mallInteractor.fetchMallStoreCategories().then(data => {
        console.log(data, '成功')
        let list = data.categories
        this.category_id = list[0].id
        list.forEach((item) => {
          item['text'] = item.name
          item['value'] = item.id
        })
        this.option2 = list
      })
    },
    pushDetail(item) {
      this.$router.push({ path: '/mallItemDetail/' + item.id})
    }
  },
  computed: {

  },
  mounted () {
    this.list = []
    this.loadFetch()
    this.fetchInfo()
  },
  created () {
    let o1 = this.$route.query
    let o2 = this.query
    var obj = Object.assign(o1, o2);
    this.query = obj
    this.query.max_income = undefined
    this.query.min_income = undefined
    
  },

}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>

.header-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.header-item-s {
  display: flex;
  align-items: center;
}
.header-item-t {
  color: #FF981D;
  font-weight: bold;
  padding-left: 5px;
}
.header-item-d {
  color: gray;
  padding-top: 5px;
  font-size: 14px;
}

footer {
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 54px;
}
.orange-button {
  background-color: #FF9000  !important;
  color: white !important;
  border: 1px solid #FF9000 !important;
}
.ssss >>>.prize-list{
  color: black
}

.my-swipe .van-swipe-item {
  color: #fff;
  font-size: 20px;
  text-align: center;
  /* background-color: #39a9ed; */
}
.van-grid-item__content {
  padding: 4px 8px;
}
.price {
  color: #ff9000;
  font-weight: bold;
}
.info-title {
  font-size: 23px;
  color: white;
}
.info-des {
  font-size: 12px;
  color: white;
}
.goods-name {
  width: 90%;
  overflow: hidden;

text-overflow: ellipsis;

display: -webkit-box;

-webkit-line-clamp: 2;

overflow:hidden;

/*! autoprefixer: off */

-webkit-box-orient: vertical;
}
</style>
