<template>
  <div>
    <van-form @submit="onSubmit">
      <van-cell-group class="cell-group">
        <van-field
          v-model="name"
          required
          label="收货人"
          type="name"
          name="name"
          placeholder="请输入收货人名字"
          input-align="right"
          :border="false"
          :rules="[{ pattern: patternName, required: true,  message: '请输入真实姓名' }]"
        />
        <van-field
          v-model="phone"
          required
          clearable
          label="手机号码"
          type="number"
          input-align="right"
          maxlength="11"
          placeholder="请输入手机号码"
          name="mobile"
          :rules="[{ pattern, message: '请输入正确的手机号码' }]"
        />
        <van-cell value-class="region-value" required name="region" :value="region" is-link @click="showArea = true" >
          <template #title>
            <span class="custom-title">地区</span>
          </template>
        </van-cell>
        <van-popup v-model="showArea" position="bottom">
          <!-- <van-area title="选择地区" :area-list="areaList" @confirm="confirmArea" /> -->
          <van-picker ref="area" show-toolbar title="选择地区" :columns="areaList" @cancel="showArea = false" value-key="name"  @confirm="onConfirm" :loading="loading" />
        </van-popup>
        <van-field
          v-model="detail"
          required
          label="详细地址"
          type="text"
          name="detail"
          input-align="right"
          placeholder="例：xx街道x号楼xx室"
          :border="false"
          :rules="[{ required: true, message: '详细地址' }]"
        />
        <!-- <van-field label-width="120px" input-align="right" name="is_default" label="设置为默认地址">
          <template #input>
            <van-switch v-model="is_default" size="20" />
          </template>
        </van-field> -->
      </van-cell-group>
      <div style="margin: 16px;">
        <van-button round block class="s-button" native-type="submit">
          提交
        </van-button>
      </div>
    </van-form>
    <div style="margin: 16px;">
      <van-button v-if="id" round block @click.stop="deleteAddress">
        删除地址
      </van-button>
    </div>
  </div>
</template>

<script>

import { mallInteractor } from '@/core'
import store from '@/store'
import { Dialog, Toast } from 'vant';

export default {
  name: 'MallAddressEdit',
  data () {
    return {
      name: undefined,
      phone: undefined,
      region: undefined,
      detail: undefined,
      showArea: false,
      is_default: false,
      areaList: undefined,
      loading: true,
      id: undefined,
      pattern: /^1[3456789]\d{9}$/,
      patternName: /^[\u4E00-\u9FA5A-Za-z]{2,20}$/,
      type: undefined
    }
  },

  methods: {
    onSubmit(values) {
      console.log(values)
      values.is_default = values.is_default ? 1:0
      values['region'] = this.region
      if (this.id) {
        values['id'] = this.id
      }
      if (!this.region) {
        Toast('请选择地区')
        return
      }
      if (!this.detail) {
        Toast('请填写详细地址')
        return
      }
      mallInteractor.fetchMallCreateAddress(values).then(data => {
        this.$router.back(-1)
      })
    },
    onConfirm(value) {
      console.log(value)
      this.region = value[0] + ' ' + value[1] + ' ' + value[2]
      this.showArea = false
    },
    fetchAreaList() {
      var _this = this
      mallInteractor.fetchMallGetAllAreas().then(data => {
        _this.areaList = data
        _this.loading = false
      })
    },
  },
  computed: {

  },
  mounted () {
    
  },
  created () {
    if (this.$route.query) {
      this.type = this.$route.query.type
      this.name = this.$route.query.name
      this.phone = this.$route.query.mobile
      this.region = this.$route.query.region
      this.detail = this.$route.query.detail
      this.is_default = this.$route.query.is_default == 1 ? true : false
      this.id = this.$route.query.id
    }
    this.fetchAreaList()
  },

}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.bg {
  background-color: #f3f3f3;
}
footer {
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 54px;
}
.orange-button {
  background-color: #FF9000  !important;
  color: white !important;
  border: 1px solid #FF9000 !important;
}

::v-deep .van-switch--on {
  background-color: #F7941E;
}
.region-value {
  color: black;
}
.cell-group {
  width: 100%;
}
.s-button {
  background-color: #F7941E;
  border: 1px solid #F7941E;
  color: white;
}

</style>

