<template>
  <div>
    <div v-if="detail">
      <van-panel title="物流信息">
        <van-cell-group>
          <van-cell title="快递公司" :value="detail.com" :border="false" />
          <van-cell title="快递单号" :value="detail.nu" :border="false" />
        </van-cell-group>
        <van-steps
          :active="active"
          direction="vertical"
          active-color="#F7941E"
        >
          <van-step v-for="(item, index) in steps" :key="index">
            <h4>{{ item.context }}</h4>
            <p>{{ item.time }}</p>
          </van-step>
        </van-steps>
      </van-panel>
      <div v-if="steps.length == 0" class="steps">暂无最新物流信息</div>
    </div>
  </div>
</template>

<script>
import { mallInteractor } from '@/core'
import store from '@/store'
import { Dialog, Toast } from 'vant';
export default {
  name: 'MallExpress',
  props: {},
  data() {
    return {
      no: undefined,
      detail: undefined,
      steps: [],
      active: 0
    }
  },
  created() {
    this.no = this.$route.query.no
    this.logisticsTracking({ no: this.no })
  },
  mounted() {},
  methods: {
    logisticsTracking(query) {
      var _this = this
      mallInteractor.fetchMallGetLogisticsTracking(query).then(data => {
        _this.detail = data
          var steps = []
          if (data.data) {
            data.data.forEach((dict) => {
              const item = {
                context: dict.context,
                time: dict.time
              }
              steps.push(item)
            })
            _this.steps = steps
          }
      })
    }
  }
}
</script>

<style scoped>
.steps{
  background-color: transparent;
  text-align: center;
  margin-top: 30px;
}
</style>
