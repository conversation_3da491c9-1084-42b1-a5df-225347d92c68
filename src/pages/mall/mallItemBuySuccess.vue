<template>
  <div class="bg">
    <img class="icon" slot="img" style="margin-top: 40px; width: 150px; height:150px" src='@/assets/success.png'/>
    <div style="margin-top: 20px;">兑换成功!</div>
    <div style="color: #f99000; font-size: 18px;margin-top: 20px;">{{title}}</div>
    <div>
      <van-button round type="info" color="#f99000"  style="margin-top: 20px;width: 200px" @click="popToRoot">返回首页</van-button>
    </div>
    
  </div>
</template>

<script>

export default {
  name: 'MallItemBuySuccess',
  data () {
    return {
      title: undefined
    }
  },

  methods: {
    popToRoot() {
      this.$router.replace(({ path: '/mallList'}))
    }
  },
  computed: {

  },
  mounted () {
  },
  created () {
    console.log(this.$route.query)
    this.title = this.$route.query.totalAmount
  },
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.bg {
  text-align: center;
  margin-top: 50px;
}
</style>

