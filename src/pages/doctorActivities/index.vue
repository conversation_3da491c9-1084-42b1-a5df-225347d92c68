<template>
  <div>
    <van-dropdown-menu style="margin: 0px 10px" active-color="#ff9000">
      <van-dropdown-item v-model="value1" :options="option1" @change="fetchPromoterDoctorActivities({month_type: value1})"/>
    </van-dropdown-menu>
    <div class="cell" v-for="item in list" :key="item.id" @click="onClick(item)">
      <van-cell-group inset>
        <van-cell :title="item.name" :label="item.start_time" >
           <template #icon>
            <van-icon :name="item.url" size="40" style="line-height: 60px;padding: 0px 4px;"/>
          </template>
        </van-cell>
      </van-cell-group>
    </div>
  </div>
</template>

<script>
import { indexInteractor } from '@/core'
export default {
  name: 'DoctorActivities',
  props: {},
  data() {
    return {
      list: [],
      value1: 1,
      option1: [
        { text: '本月', value: 1 },
        { text: '上月', value: 2 }
      ],
    }
  },
  created() {},
  mounted() {
    this.fetchPromoterDoctorActivities()
  },
  methods: {
    async fetchPromoterDoctorActivities(query){
      try {
        let q = Object.assign({}, query, query)
        await indexInteractor.fetchPromoterDoctorActivities(q).then(data => {
          this.list = data.list
        })
      } catch (error) {
        console.log(error)
      }
    },
    onClick(item) {
      window.location.href = item.redirect_url
    }
  }
}
</script>

<style lang="scss" scoped>
.cell {
  padding: 0px 0 0 0 ;
}
::v-deep .van-cell-group--inset {
  margin: 0 8px;
}
::v-deep .van-dropdown-menu__item {
  justify-content: flex-end;
}
::v-deep .van-dropdown-menu__bar {
  background-color: transparent;
  box-shadow: none;
}
</style>
