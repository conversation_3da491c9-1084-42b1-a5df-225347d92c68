<template>
  <div>
    <div style="line-height: 40px;background-color: white;padding-left: 20px;">统计日期： {{query.begin_time + '至' + query.end_time}}
      <van-button type="default" style="width: 20%;border: 0px;height: 54px;" @click="showPopup = true">筛选</van-button>
    </div>
    <div style="display: flex;align-items: center;">
        <!-- <van-search
          v-model="query.keywords"
          placeholder="请输入医生姓名"
          @search="onSearch"
          style="width: 80%;"
        /> -->
        
      </div>
      <van-pull-refresh v-model="loading" @refresh="onRefresh">
        <van-list
          v-model="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
          offset="150"
        >
        <div class="cell" v-for="item in list" :key="item.doctor_id">
          <AvatarButton :url="item.figure_file ? item.figure_file.url : ''" :statusString="item.status_name" :status="item.status"/>
          <div style="width:100%">
            <van-cell :title="((item.name ? item.name : '') + ' ' + item.mobile)" is-link @click="clickCell(item)" />
            <van-cell :title="'对接：' + item.promoter_name" :value="item.created_at.split(' ')[0] + '邀请'" />
          </div>
        </div>
        </van-list>
      </van-pull-refresh>
      <van-popup
        v-model="showPopup"
        position="top"
        :close-on-click-overlay = false
        >
        <van-form>
          <van-field
            readonly
            clickable
            name="picker"
            :value="query.begin_time "
            label="开始时间"
            placeholder="点击选择开始时间"
            @click="showPopupBeginDatePicker = true"
          />
          <van-field
            readonly
            clickable
            name="picker2"
            :value="query.end_time "
            label="结束时间"
            placeholder="点击选择结束时间"
            @click="showPopupEndDatePicker = true"
          />
          <van-field name="radio" label="医生状态">
            <template #input>
              <van-radio-group v-model="query.status" direction="horizontal">
                <van-radio style="height: 30px" name="4" checked-color="#F7941E">全部</van-radio>
                <van-radio style="height: 30px" name="3" checked-color="#F7941E">未认证</van-radio>
                <van-radio style="height: 30px" name="1" checked-color="#F7941E">已认证</van-radio>
                <van-radio style="height: 30px" name="2" checked-color="#F7941E">拒绝</van-radio>
              </van-radio-group>
            </template>
          </van-field>
        </van-form>
        <div>
          <van-row>
            <van-col span="12">
              <van-button class="width-button" color="#F7941E" type="primary" @click="filterAction">确定</van-button>
            </van-col>
            <van-col span="12">
              <van-button class="width-button" color="#D4D4D4" type="primary" @click="cleanFilterAction">清除条件</van-button>
            </van-col>
          </van-row>
        </div>
      </van-popup>
      <van-popup
        v-model="showPopupBeginDatePicker"
        position="bottom"
      >
        <van-datetime-picker
          v-model="begin_time"
          type="date"
          :formatter="formatter"
          :min-date="minDate"
          :max-date="maxDate"
          @confirm="confirmPopupBeginDatePicker"
          @cancel="showPopupBeginDatePicker = false"
        />
      </van-popup>
      <van-popup
        v-model="showPopupEndDatePicker"
        position="bottom"
      >
        <van-datetime-picker
          v-model="end_time"
          type="date"
          :formatter="formatter"
          :min-date="minDate"
          :max-date="maxDate"
          @confirm="confirmPopupEndDatePicker"
          @cancel="showPopupEndDatePicker = false"
        />
      </van-popup>
  </div>
</template>

<script>
import { doctorInteractor } from '@/core'
import AvatarButton from '@/components/AvatarButton'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn' // ES 2015 

dayjs.locale('zh-cn')
export default {
  name: 'StatisticsDoctorList',
  props: {},
  components: {
    AvatarButton
  },
  data() {
    return {
      d: undefined,
      list: [],
      query: {
        begin_time: undefined,
        end_time: undefined,
        page: 0,
        size: 10,
        type: undefined,
        status: '',
        level: undefined
      },
      begin_time: undefined,
      end_time: undefined,
      showPopup: false,
      minDate: new Date(2019, 0, 1),
      maxDate: new Date(),
      showPopupBeginDatePicker: false,
      showPopupEndDatePicker: false,
      loading: false,
      finished: false,
      refreshing: false
    }
  },
  created() {
    let d = this.$route.query
    this.begin_time = d.begin_time
    this.end_time = d.end_time
    this.query.status = d.status.toString()
    this.query.begin_time = d.begin_time
    this.query.end_time = d.end_time
    this.query.type = d.type
    if (d.level) {
      this.query.level = d.level
    }
    console.log(this.$route.query)
  },
  mounted() {},
  methods: {
    formatter(type, val) {
      if (type === 'year') {
        return `${val}年`;
      } else if (type === 'month') {
        return `${val}月`
      } else {
        return `${val}日`
      }
      return val;
    },
    onSearch(k) {
      console.log(k)
      this.query.page = 1
      this.list = []
      this.fetchDoctorReportList(this.query)
    },
    clickCell(item) {
      if (item.status == 0) {
        Toast.fail('该医生未认证')
      } else if (item.status == 2) {
        Toast.fail('该医生认证已拒绝')
      } else {
        this.$router.push({path:'/doctorDetail',query: item})
      }
      
    },
    confirmPopupBeginDatePicker(t) {
      this.query.begin_time = dayjs(t).format('YYYY-MM-DD')
      this.showPopupBeginDatePicker = false
    },
    confirmPopupEndDatePicker(t) {
      this.query.end_time = dayjs(t).format('YYYY-MM-DD')
      this.showPopupEndDatePicker = false
    },
    filterAction() {
      this.showPopup = false
      this.query.page = 1
      this.list = []
      this.fetchDoctorReportList(this.query)
    },
    cleanFilterAction() {
      // this.end_time = undefined
      // this.begin_time = undefined
      let d = this.$route.query
      this.begin_time = d.begin_time
      this.end_time = d.end_time
      this.showPopup = false
      this.list = []
      this.query = {
        page: 1,
        size: 10
      },
      this.query.begin_time = this.begin_time
      this.query.end_time = this.end_time
      this.fetchDoctorReportList(this.query)
    },
    async fetchDoctorReportList(query) {
      try {
        let q = Object.assign({}, query, query)
        if (this.begin_time) {
          q.begin_time = dayjs(this.begin_time).format('YYYY-MM-DD')
          q.end_time = dayjs(this.end_time).format('YYYY-MM-DD')
        }
        console.log(q)
        await doctorInteractor.fetchDoctorReportList(q).then(data => {
          if (this.query.page === 1) {
            this.list = data.list
          } else {
            this.list = [...this.list, ...data.list]
          }
          if (data.total === this.list.length) {
            this.finished = true
          } else {
            this.finished = false
          } 
          console.log(this.list)
          this.loading = false
        })
        
        
        
      } catch (error) {
        console.log(error)
      }
    },
    onLoad() {
      // 异步更新数据
      this.loading = true
      this.query.page = this.query.page + 1
      console.log('onLoad')
      this.fetchDoctorReportList(this.query)
    },
    onRefresh() {
      // 清空列表数据
      this.finished = false
      console.log('onRefresh')
      console.log(this.query)
      // 重新加载数据
      // 将 loading 设置为 true，表示处于加载状态
      this.loading = true
      this.query.page = 1
      this.list = []
      this.fetchDoctorReportList(this.query)
    }
  }
}
</script>

<style lang="scss" scoped>
.cell {
  background-color: white;
  margin: 10px;
  height: 88px;
  border-radius: 10px;
  display: flex;
}
::v-deep .van-cell {
  background-color: transparent;
}
</style>
