<template>
  <div>
    <van-field
      v-model="forgot.mobile"
      label="手机号"
      placeholder="请输入手机号"
    />
    <van-field
      v-model="forgot.code"
      center
      clearable
      label="短信验证码"
      placeholder="请输入短信验证码"
    >
      <template #button>
        <van-button size="small" type="primary" @click="sendCodeAction" round plain :disabled="!canClick">{{content}}</van-button>
      </template>
    </van-field>
    <van-field v-model="forgot.password" label="密码" placeholder="请输入6位以上的密码" />
    <div class="bottom-button">
      <van-button class="v-button" round color="linear-gradient(to right, #F7941E, #F24316)" @click="handleForgot" >提交</van-button>
    </div>
  </div>
</template>

<script>

import { loginInteractor } from '@/core'
import { Toast, Dialog } from 'vant'

export default {
  name: 'Forgot',
  props: {},
  data() {
    return {
      forgot: {
        mobile: '',
        code: '',
        password: ''
      },
      content: '发送验证码',
      totalTime: 60,
      canClick: true  //添加canClick
    }
  },
  created() {},
  mounted() {},
  methods: {
    handleForgot() {
      if (this.forgot.password.length >= 6 &&  this.forgot.code.length == 4) {
        this.resetForgotPassword()
      } else if (this.forgot.code.length != 4) {
        Toast.fail('请输入正确的验证码')
      } else {
        Toast.fail('请输入正确的密码')
      }
      
    },
    async resetForgotPassword() {
      try {
        const test = await loginInteractor.resetForgotPassword(this.forgot)
        Toast.success('重置密码已生效')
        this.$router.back(-1)
      } catch (error) {
        console.log(error)
      }
    },
    sendCodeAction() {
      if (this.forgot.mobile && this.forgot.mobile.length == 11) {
        const sendcode = loginInteractor.sendSmsCode({mobile: this.forgot.mobile, type:2}).then( data => {
          Toast.success('已发送验证码')
          this.countDown()
        })
      } else {
        Toast.fail('请输入正确的手机号码')
      }
      
    },
    countDown() {
      if (!this.canClick) return   //改动的是这两行代码
      this.canClick = false
      this.content = this.totalTime + 's后重新发送'
      let clock = window.setInterval(() => {
        this.totalTime--
        this.content = this.totalTime + 's后重新发送'
        if (this.totalTime < 0) {
          window.clearInterval(clock)
          this.content = '重新发送验证码'
          this.totalTime = 60
          this.canClick = true   //这里重新开启
        }
      },1000)
    }
  }
}
</script>

<style lang="scss" scoped>
.bottom-button {
  margin-top: 50px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  width: 100%;
}
.forgot {
  text-align: center;
}
.v-button {
  width: 200px !important; 
}
</style>
