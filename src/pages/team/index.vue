<template>
  <div>
    <van-tabs v-model="active" color="#F7941E">
      <van-tab title="团队成员">
        <div style="display: flex;align-items: center;">
          <van-search
            v-model="query.keyword"
            placeholder="请输入业务员姓名"
            @search="onSearch"
            style="width: 80%;"
          />
          <van-button type="default" style="width: 20%;border: 0px;height: 54px;" @click="showPopup = true">筛选</van-button>
        </div>
        <div v-if="info.role_id == 8" style="text-align: right; font-size: 12px; margin: 4px 10px">成员等级:{{query.level == '1' ? '一级' : '二级'}}</div>
        <van-pull-refresh v-model="loading" @refresh="onRefresh" ref="pull-refresh">
        <van-list
          v-model="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
          offset="150"
        >
        <div class="cell" v-for="(item, index) in list" :key="index">
          <van-cell-group>
            <van-cell is-link @click="clickCell(item)">
              <template #title>
                <span class="custom-title">{{item.name}}</span>
                <van-button style="margin-left:10px" color="#f6d21c" round size="mini">{{item.level}}</van-button>
              </template>
            </van-cell>
          </van-cell-group>
        </div>
        </van-list>
      </van-pull-refresh>
      </van-tab>
      <van-tab title="业绩排名">
        <van-empty image="search" description="待开放" />
      </van-tab>
    </van-tabs>
    <van-popup
        v-model="showPopup"
        position="top"
        :close-on-click-overlay = false
        >
        <van-form>
          <van-field name="radio" label="等级">
            <template #input>
              <van-radio-group v-model="query.level" direction="horizontal">
                <van-radio style="height: 30px" v-for="(item, index) in radios" :key="index" :name="item.level" checked-color="#F7941E">{{item.title}}</van-radio>
              </van-radio-group>
            </template>
          </van-field>
        </van-form>
        <div>
          <van-row>
            <van-col span="12">
              <van-button class="width-button" color="#F7941E" type="primary" @click="filterAction">确定</van-button>
            </van-col>
            <van-col span="12">
              <van-button class="width-button" color="#D4D4D4" type="primary" @click="cleanFilterAction">清除条件</van-button>
            </van-col>
          </van-row>
        </div>
      </van-popup>
    <router-view />
    <van-tabbar route active-color="#F7941E">
      <van-tabbar-item replace to="/home" icon="home-o">
        首页
      </van-tabbar-item>
      <van-tabbar-item v-if="info && info.level_id != 1" replace to="/doctors" icon="manager-o">
        医生
      </van-tabbar-item>
      <van-tabbar-item v-if="info && info.level_id != 4" replace to="/team" icon="friends-o">
        我的团队
      </van-tabbar-item>
      <van-tabbar-item replace to="/mine" icon="user-circle-o" dot >
        我的
      </van-tabbar-item>
    </van-tabbar>
  </div>
  
</template>

<script>
import { teamInteractor, partnerInteractor } from '@/core'
import { readFromLocalStorage } from '@/core/services/cache'

export default {
  name: 'Team',
  props: {},
  data() {
    return {
      info: undefined,
      active: 0,
      list: [],
      query: {
        page: 0,
        size: 30,
        keyword: '',
        level: undefined,
      },
      radios: [],
      loading: false,
      finished: false,
      refreshing: false,
      showPopup: false,
    }
  },
  created() {
    let info = readFromLocalStorage('info')
    this.info = info
    switch (info.level_id){
      case 1:
        this.radios = [
          {
            level: '2',
            title: '区总'
          },
          {
            level: '3',
            title: '地区经理'
          },
          {
            level: '4',
            title: '业务员'
          },
        ]
      break;
    case 2:
        this.radios = [
          {
            level: '3',
            title: '地区经理'
          },
          {
            level: '4',
            title: '业务员'
          },
        ]
      break;
    case 3:
        this.radios = [
          {
            level: '4',
            title: '业务员'
          },
        ]
      break;
    }
    if(info.role_id == 8) {
      this.radios = [
          {
            level: '1',
            title: '一级'
          },
          {
            level: '2',
            title: '二级'
          },
        ]
      this.query.level = 1
    }
  },
  mounted() {},
  methods: {
    filterAction() {
      this.showPopup = false
      this.query.page = 1
      this.query.size = 30
      this.list = []
      this.finished = false
      this.loading = true
      this.getTeamList(this.query)
    },
    cleanFilterAction() {
      this.showPopup = false
      this.list = []
      this.query = {
        page: 1,
        size: 7,
        level: undefined,
        keyword: this.query.keyword
      },
      this.finished = false
      this.loading = true
      this.getTeamList(this.query)
    },
    onSearch(k) {
      console.log(k)
      this.query.page = 1
      this.list = []
      this.getTeamList(this.query)
    },
    async getTeamList(query) {
      if (this.info.role_id == 8) {
        try {
          await partnerInteractor.fetchPromoterPartners(query).then(data => {
            if (this.query.page === 1) {
              this.list = data.list
            } else {
              this.list = [...this.list, ...data.list]
            }
            if (data.total === this.list.length) {
              this.finished = true
            }
            this.loading = false
          })
        } catch (error) {
          console.log(error)
        }
      } else {
        try {
          await teamInteractor.getTeam(query).then(data => {
            if (this.query.page === 1) {
              this.list = data.list
            } else {
              this.list = [...this.list, ...data.list]
            }
            if (data.total === this.list.length) {
              this.finished = true
            }
            this.loading = false
          })
        } catch (error) {
          console.log(error)
        }
      }
      
    },
    onLoad() {
      this.loading = true
      this.query.page = this.query.page + 1
      console.log('onLoad')
      console.log(this.query)
      this.getTeamList(this.query)
    },
    onRefresh() {
      // 清空列表数据
      this.finished = false
      this.loading = true
      this.query.page = 1
      this.list = []
      this.getTeamList(this.query)
    },
    clickCell(item) {
      console.log(item)
      this.$router.push({
                    path:'/teamMember',
                    query: item
                  })
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
