<template>
  <div>
    <van-form @submit="onSubmit">
      <van-field
        v-model="name"
        name="promoter_visit_doctor_list_id"
        clickable
        label="拜访医生"
        readonly
        input-align="right"
        placeholder="点击选择"
        @click="jumpToVisitDoctor()"
      />
      <van-field
        readonly
        clickable
        name="visit_type_id"
        :value="visitType"
        label="拜访方式"
        placeholder="点击选择"
        @click="showVisitTypePicker = true"
        input-align="right"
      />
      <van-field
        v-model="content"
        name="content"
        label="拜访内容"
        placeholder="此处填写拜访内容"
        rows="3"
        autosize
        type="textarea"
        input-align="right"
        :rules="[{ required: true, message: '请填写拜访内容' }]"
      />

      <div style="margin: 16px;">
        <van-button round block type="info" native-type="submit" color="#ff9000">保存</van-button>
      </div>
    </van-form>
    <van-popup v-model="showVisitTypePicker" round position="center" style="width: 80%">
      <van-picker
        show-toolbar
        :columns="visitTypeColumns"
        value-key="name"
        @cancel="showVisitTypePicker = false"
        @confirm="onVisitTypeConfirm"
      />
    </van-popup>
  </div>
</template>

<script>
import { doctorInteractor } from '@/core'
import dayjs from 'dayjs'
import quarterOfYear from 'dayjs/plugin/quarterOfYear' // ES 2015
import 'dayjs/locale/zh-cn' // ES 2015 
import { Toast } from 'vant'
dayjs.locale('zh-cn')
dayjs.extend(quarterOfYear)

export default {
  name: 'Visit',
  props: {},
  data() {
    return {
      name: undefined,
      list: [],
      showVisitTypePicker: false,
      visitTypeColumns: [],
      visitType: undefined,
      visit_type_id: undefined,
      content: undefined,
      doctor: undefined,
    }
  },
  created() {
    
    this.$bus.$on("selectedDoctor", (selectedDoctor) => {
      console.log('selectedDoctor')
      console.log(selectedDoctor)
      this.doctor = selectedDoctor
      this.name = selectedDoctor.doctor_name
      // this.messagesCount = count
    });
  },
  mounted() {
    this.fetchPromoterVisitTypeList()
  },
  methods: {
    jumpToVisitDoctor() {
      this.$router.push({path:'/visitDoctorList'})
    },
    async fetchPromoterVisitTypeList(){
      try {
        await doctorInteractor.fetchPromoterVisitTypeList().then(data => {
          console.log(data)
          this.visitTypeColumns = data.type_list
        })
      } catch (error) {
        console.log(error)
      }
    },
    async fetchSetPromoterVisit(q){
      try {
        await doctorInteractor.fetchSetPromoterVisit(q).then(data => {
          console.log(data)
          this.visitType = undefined
          this.visit_type_id = undefined
          this.content = undefined
          this.doctor = undefined
          this.name = undefined
          this.$bus.emit('visitRefresh', 'r')
          this.$router.back()
        })
      } catch (error) {
        console.log(error)
      }
    },
    onClick(item) {
      if (item.doctor_id) {
        this.$router.push({path:'/doctorDetail',query: { doctor_id : item.doctor_id}})
      }
    },
    onVisitTypeConfirm(item) {
      this.visitType = item.name
      this.showVisitTypePicker = false
      this.visit_type_id = item.id
    },
    onSubmit(values) {
      console.log('submit0', values);
      values.promoter_visit_doctor_list_id = this.doctor.id
      values.visit_type_id = this.visit_type_id
      if (!this.doctor) {
        Toast.fail('请选择拜访医生')
        return
      }
      if (!values.visit_type_id || values.visit_type_id.length == 0) {
        Toast.fail('请选择拜访方式')
        return
      }
      if (!values.content || values.content.length == 0) {
        Toast.fail('请填写拜访内容')
        return
      }
      console.log('submit1', values);
      this.fetchSetPromoterVisit(values)
    },
  }
}
</script>

<style lang="scss" scoped>
.cell {
  padding: 8px 0 0 0 ;
}
::v-deep .van-cell-group--inset {
  margin: 0 8px;
}
</style>
