<template>
  <div>
    <div class="cell" v-for="item in list" :key="item.id" @click="onClick(item)">
      <van-cell-group inset>
        <van-cell :title="item.name" :value="'¥' + item.amount" :label="item.invite_time + '邀请'" :center=true is-link >
           <template #icon>
              <van-icon :name="item.url" size="40" style="line-height: 60px;padding: 0px 4px;"/>
            </template>
        </van-cell>
      </van-cell-group>
    </div>
  </div>
</template>

<script>
import { indexInteractor } from '@/core'
export default {
  name: '<PERSON><PERSON>an<PERSON>',
  props: {},
  data() {
    return {
      list: [],
    }
  },
  created() {},
  mounted() {
    this.fetchPromoterDoctorRank()
  },
  methods: {
    async fetchPromoterDoctorRank(query){
      try {
        let q = Object.assign({}, query, query)
        await indexInteractor.fetchPromoterDoctorRank(q).then(data => {
          this.list = data.list
        })
      } catch (error) {
        console.log(error)
      }
    },
    onClick(item) {
      if (item.doctor_id) {
        this.$router.push({path:'/doctorDetail',query: { doctor_id : item.doctor_id}})
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.cell {
  padding: 8px 0 0 0 ;
}
::v-deep .van-cell-group--inset {
  margin: 0 8px;
}
</style>
