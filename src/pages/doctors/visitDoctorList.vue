<template>
  <div>
    <div style="display: flex;align-items: center;">
      <van-search
        v-model="query.keywords"
        placeholder="请输入医生姓名"
        @search="onSearch"
        style="width: 100%;"
      />
    </div>
    <div style="display:flex;justify-content: center;background-color:white;margin:6px 0;">
      <div style="padding:10px;font-size:14px;" @click="jumpToVisitDoctorEdit()">添加医生</div>
    </div>
    <van-pull-refresh  v-model="loading" @refresh="onRefresh">
      <van-list
        v-model="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
        offset="150"
      >
      <van-cell-group>
        <van-cell v-for="(item, index) in list" :key="index" :label="item.company"  @click="selectedDoctor(item)">
        <!-- 使用 title 插槽来自定义标题 -->
          <template #title>
            <span class="custom-title">{{item.doctor_name}}</span>
            <span  style="padding-left:10px">{{item.mobile}}</span>
          </template>
        </van-cell>
      </van-cell-group>
      
      </van-list>
    </van-pull-refresh>
    <van-dialog v-model="showDialog" title="添加医生" show-cancel-button confirmButtonColor="#ff9000" @confirm="$refs.form.submit()">
      <van-form ref="form" @submit="dialogConfirm">
        <van-field
          v-model="doctorQuery.doctor_name"
          name="doctor_name"
          label="医生姓名"
          placeholder="医生姓名"
          input-align="right"
          :rules="[{ required: true, message: '请填写医生姓名' }]"
        />
        <van-field
          v-model="doctorQuery.mobile"
          name="mobile"
          label="联系电话"
          type="tel" 
          placeholder="联系电话"
          input-align="right"
        />
        <!-- :rules="[{ required: true, message: '请填写联系电话' }]" -->
        <van-field
          v-model="doctorQuery.company"
          name="company"
          label="执业机构"
          placeholder="执业机构"
          input-align="right"
          :rules="[{ required: true, message: '请填写执业机构' }]"
        />
      </van-form>
    </van-dialog>
  </div>
</template>

<script>
import { doctorInteractor } from '@/core'
import dayjs from 'dayjs'
import quarterOfYear from 'dayjs/plugin/quarterOfYear' // ES 2015
import 'dayjs/locale/zh-cn' // ES 2015 
import { Toast } from 'vant'
dayjs.locale('zh-cn')
dayjs.extend(quarterOfYear)

export default {
  name: 'VisitDoctorList',
  props: {},
  data() {
    return {
      doctorQuery: {
        doctor_name: undefined,
        mobile: undefined,
        company: undefined,
      },
      loading: false,
      list: [],
      finished: false,
      query: {
        page: 0,
        size: 10,
        keywords: '',
      },
      showDialog: false
    }
  },
  created() {
    
  },
  mounted() {
  },
  methods: {
    selectedDoctor(item) {
      this.$bus.emit('selectedDoctor', item)
      this.$router.back()
    },
    dialogConfirm() {
      console.log(this.doctorQuery)
      this.fetchSetPromoterVisitDoctor()
    },
    fetchSetPromoterVisitDoctor() {
      doctorInteractor.fetchSetPromoterVisitDoctor(this.doctorQuery).then(data => {
        this.onRefresh()
        this.doctorQuery.doctor_name = undefined
        this.doctorQuery.company = undefined
        this.doctorQuery.mobile = undefined
      })
    },
    jumpToVisitDoctorEdit() {
      this.$router.push({path:'/visitDoctorEdit'})
    },
    jumpToVisitDoctor() {
      this.$router.push({path:'/doctorDetail'})
    },
    onSearch() {
      this.query.page = 1
      this.list = []
      this.fetchPromoterVisitDoctorList(this.query)
    },
    onLoad() {
      // 异步更新数据
      // setTimeout 仅做示例，真实场景中一般为 ajax 请求
      this.loading = true
      this.query.page = this.query.page + 1
      console.log('onLoad')
      this.fetchPromoterVisitDoctorList(this.query)
    },
    onRefresh() {
      // 清空列表数据
      this.finished = false
      console.log('onRefresh')
      // 重新加载数据
      // 将 loading 设置为 true，表示处于加载状态
      this.loading = true
      this.query.page = 1
      this.list = []
      this.fetchPromoterVisitDoctorList(this.query)
    },
    async fetchPromoterVisitDoctorList(q) {
      await doctorInteractor.fetchPromoterVisitDoctorList(q).then(data => {
          if (this.query.page === 1) {
            this.list = data.promoter_visit_doctor_list
          } else {
            this.list = [...this.list, ...data.promoter_visit_doctor_list]
          }
          if (data.total <= this.list.length) {
            this.finished = true
          }
          console.log(this.list)
          this.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.cell {
  padding: 8px 0 0 0 ;
}
::v-deep .van-cell-group--inset {
  margin: 0 8px;
}
</style>
