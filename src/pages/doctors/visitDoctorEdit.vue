<template>
  <div>
    <van-form ref="form" @submit="onSubmit">
      <van-field
        v-model="doctorQuery.doctor_name"
        required
        name="doctor_name"
        label="医生姓名"
        placeholder="医生姓名"
        input-align="right"
        :rules="[{ required: true, message: '请填写医生姓名' }]"
      />
      <van-field
        v-model="doctorQuery.mobile"
        name="mobile"
        label="联系电话"
        type="tel" 
        placeholder="联系电话"
        input-align="right"
      />
      <!-- :rules="[{ required: true, message: '请填写联系电话' }]" -->
      <van-field
        v-model="doctorQuery.company"
        name="company"
        required
        label="执业机构"
        placeholder="执业机构"
        input-align="right"
        :rules="[{ required: true, message: '请填写执业机构' }]"
      />
      <van-cell value-class="region-value" required name="region" is-link @click="showArea = true" >
        <template #title>
          <span  style="color: #646566;">机构所在地区</span>
        </template>
        <template #right-icon >
          <span style="color: gray" v-if="!region">点击选择地区</span>
          <span class="custom-title" v-else>{{region}}</span>
        </template>
      </van-cell>
      <van-popup v-model="showArea" position="bottom">
        <van-picker ref="area" show-toolbar title="选择地区" :columns="areaList" @cancel="showArea = false" value-key="name"  @confirm="onConfirm" :loading="loading" />
      </van-popup>
      <van-field
        v-model="doctorQuery.company_address"
        required
        label="详细地址"
        type="text"
        name="detail"
        input-align="right"
        placeholder="例：xx街道x号楼xx室"
        :border="false"
        :rules="[{ required: true, message: '详细地址' }]"
      />
      <div style="margin: 16px;">
        <van-button round block class="s-button" native-type="submit">
          提交
        </van-button>
      </div>
    </van-form>
  </div>
</template>

<script>
import { doctorInteractor, indexInteractor } from '@/core'
import dayjs from 'dayjs'
import quarterOfYear from 'dayjs/plugin/quarterOfYear' // ES 2015
import 'dayjs/locale/zh-cn' // ES 2015 
import { Toast } from 'vant'
dayjs.locale('zh-cn')
dayjs.extend(quarterOfYear)

export default {
  name: 'VisitDoctorList',
  props: {},
  data() {
    return {
      doctorQuery: {
        doctor_name: undefined,
        mobile: undefined,
        company: undefined,
        company_address: undefined
      },
      showArea: false,
      areaList: undefined,
      region: undefined,
      loading:true
    }
  },
  created() {
    this.fetchAreaList()
  },
  mounted() {
  },
  methods: {
    
    onSubmit() {
      
      if (!this.region || this.region.length == 0 ) {
        Toast.fail('请选择机构所在地区')
        return
      } 
      this.doctorQuery.company_address = String(this.region) + '' + String(this.doctorQuery.company_address)
      console.log(this.doctorQuery)
      this.fetchSetPromoterVisitDoctor()
    },
    onConfirm(value) {
      this.region = value[0] + '' + value[1] + '' + value[2]
      this.showArea = false
    },
    fetchSetPromoterVisitDoctor() {
      doctorInteractor.fetchSetPromoterVisitDoctor(this.doctorQuery).then(data => {
        this.$router.back()
      })
    },
    fetchAreaList() {
      indexInteractor.fetchGetAreas().then(data => {
        this.areaList = data
        this.loading = false
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.cell {
  padding: 8px 0 0 0 ;
}
::v-deep .van-cell-group--inset {
  margin: 0 8px;
}

.region-value {
  color: black;
}
.cell-group {
  width: 100%;
}
.s-button {
  background-color: #F7941E;
  border: 1px solid #F7941E;
  color: white;
}
</style>
