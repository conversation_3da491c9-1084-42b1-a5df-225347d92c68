<template>
  <div>
    <van-form @submit="onSubmit">
      <van-field
        v-model="name"
        name="doctor_id"
        label="跟进医生"
        placeholder="用户名"
        readonly
        input-align="right"
      />
      <van-field
        readonly
        clickable
        name="follow_type_id"
        :value="followType"
        label="跟进类型"
        placeholder="点击选择类型"
        @click="showFollowTypePicker = true"
        input-align="right"
      />
      <van-field
        readonly
        clickable
        name="follow_time"
        :value="follow_time"
        label="跟进时间"
        placeholder="点击选择时间"
        @click="showDatePicker = true"
        input-align="right"
      />
      <van-field
        v-model="content"
        name="content"
        label="跟进内容"
        placeholder="此处填写跟进内容"
        rows="3"
        autosize
        type="textarea"
        input-align="right"
        :rules="[{ required: true, message: '请填写跟进内容' }]"
      />

      <div style="margin: 16px;">
        <van-button round block type="info" native-type="submit" color="#ff9000">保存</van-button>
      </div>
    </van-form>
    <van-popup v-model="showDatePicker" position="center" style="width: 80%">
      <van-datetime-picker
        v-model="currentDate"
        type="date"
        @confirm="dateOnConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>
    <van-popup v-model="showFollowTypePicker" round position="center" style="width: 80%">
      <van-picker
        show-toolbar
        :columns="followTypeColumns"
        value-key="name"
        @cancel="showFollowTypePicker = false"
        @confirm="onFollowTypeConfirm"
      />
    </van-popup>
  </div>
</template>

<script>
import { doctorInteractor } from '@/core'
import dayjs from 'dayjs'
import quarterOfYear from 'dayjs/plugin/quarterOfYear' // ES 2015
import 'dayjs/locale/zh-cn' // ES 2015 
import { Toast } from 'vant'
dayjs.locale('zh-cn')
dayjs.extend(quarterOfYear)

export default {
  name: 'Follow',
  props: {},
  data() {
    return {
      name: undefined,
      list: [],
      showFollowTypePicker: false,
      followTypeColumns: [],
      followType: undefined,
      showDatePicker: false,
      currentDate: new Date(),
      follow_type_id: undefined,
      doctor_id: undefined,
      content: undefined,
      follow_time: undefined
    }
  },
  created() {
    this.doctor_id = this.$route.query.doctor_id
    let name = this.$route.query.doctor_name
    console.log(name)
    this.name = name
    
  },
  mounted() {
    this.fetchPromoterFollowType()
  },
  methods: {
    async fetchPromoterFollowType(){
      try {
        await doctorInteractor.fetchPromoterFollowType().then(data => {
          console.log(data)
          this.followTypeColumns = data.type_list
        })
      } catch (error) {
        console.log(error)
      }
    },
    async fetchSetPromoterFollow(q){
      try {
        await doctorInteractor.fetchSetPromoterFollow(q).then(data => {
          console.log(data)
          this.follow_type_id = undefined
          this.followType = undefined
          this.content = undefined
          this.follow_time = undefined
          this.$bus.emit('selectedFollow', 'r')
          this.$router.back()
        })
      } catch (error) {
        console.log(error)
      }
    },
    onClick(item) {
      if (item.doctor_id) {
        this.$router.push({path:'/doctorDetail',query: { doctor_id : item.doctor_id}})
      }
    },
    dateOnConfirm(item) {
      let t = dayjs(item).format('YYYY-MM-DD')
      this.follow_time = t
      this.showDatePicker = false;
    },
    onFollowTypeConfirm(item) {
      this.followType = item.name
      this.showFollowTypePicker = false
      this.follow_type_id = item.id
    },
    onSubmit(values) {
      console.log('submit0', values);
      values.doctor_id = this.doctor_id
      values.follow_type_id = this.follow_type_id
      if (!values.follow_type_id || values.follow_type_id.length == 0) {
        Toast.fail('请选择跟进类型')
        return
      }
      if (!values.follow_time || values.follow_time.length == 0) {
        Toast.fail('请选择跟进时间')
        return
      }
      console.log('submit1', values);
      this.fetchSetPromoterFollow(values)
    },
  }
}
</script>

<style lang="scss" scoped>
.cell {
  padding: 8px 0 0 0 ;
}
::v-deep .van-cell-group--inset {
  margin: 0 8px;
}
</style>
