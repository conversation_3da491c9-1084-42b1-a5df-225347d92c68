<template>
  <div>
    <van-tabs v-model="tabActive" color="#ff9000">
      <van-tab title="我的医生">
        <div>
          <div style="display: flex;align-items: center;">
            <van-search
              v-model="query.keywords"
              placeholder="请输入医生姓名"
              @search="onSearch"
              style="width: 100%;"
            />
          </div>
          <van-dropdown-menu active-color="#ff9000">
            <van-dropdown-item v-model="query.status" :options="option" @change="statusChange"/>
            <van-dropdown-item title="邀请时间" ref="item" @open="inviteCalendarShow = true">
            </van-dropdown-item>
            <van-dropdown-item title="认证时间" ref="item" @open="credentialCalendarShow = true">
            </van-dropdown-item>
            <van-dropdown-item title="地区" ref="item" @open="areasCalendarShow = true">
            </van-dropdown-item>
          </van-dropdown-menu>
          <van-calendar v-model="inviteCalendarShow" ref="inviteCalendar" type="range" :min-date="minDate" :max-date="maxDate" color="#ff9000" title="邀请时间" @confirm="inviteOnConfirm" >
            <template #footer>
              <div v-if="query"></div>
              <div style="display: flex;flex-direction: row;justify-content: space-around;padding: 10px 0">
                <van-button round color="#ff9000" plain style="width: 150px" @click="inviteReset">重置</van-button>
                <van-button round color="#ff9000" style="width: 150px" @click="inviteOnConfirm($refs.inviteCalendar.currentDate)">确认</van-button>
              </div>
            </template>
          </van-calendar>
          <van-calendar v-model="credentialCalendarShow" ref="credentialCalendar" type="range" :min-date="minDate" :max-date="maxDate" color="#ff9000" title="认证时间" @confirm="credentialOnConfirm" >
            <template #footer>
              <div v-if="query"></div>
              <div style="display: flex;flex-direction: row;justify-content: space-around;padding: 10px 0">
                <van-button round color="#ff9000" plain style="width: 150px" @click="credentialReset">重置</van-button>
                <van-button round color="#ff9000" style="width: 150px" @click="credentialOnConfirm($refs.credentialCalendar.currentDate)">确认</van-button>
              </div>
            </template>
          </van-calendar>
          <van-popup v-model="areasCalendarShow" round position="bottom">
            <van-cascader
              v-model="areasValues"
              title=""
              active-color="#f39800"
              :options="areasList"
              @close="areasCalendarShow = false"
              @finish="areasOnFinish"
              :field-names="fieldNames"
            >
            <template #title>
              <div style="display: flex;flex-direction: row;justify-content: space-around;padding: 10px; align-items: center;">
                <div>请选择所在地区</div>
                <van-button round color="#f39800" plain style="width: 100px; margin-left: 10px;" size="small" @click="areasCredentialReset">重置</van-button>
              </div>
            </template>
          </van-cascader>
          </van-popup>
          <van-pull-refresh v-model="loading" @refresh="onRefresh">
            <van-list
              v-model="loading"
              :finished="finished"
              finished-text="没有更多了"
              @load="onLoad"
              offset="150"
            >
            <div class="cell" v-for="item in list" :key="item.doctor_id" @click="clickCell(item)" >
              <div style="display: flex">
                <div style="width:70px">
                  <AvatarButton :url="item.figure_file ? item.figure_file.url : ''" :statusString="item.status_name" :status="item.status"/>
                </div>
                <div style="width:100%">
                  <van-cell :title="((item.name ? item.name : ''))">
                    <template #title >
                      <div style="display: flex;align-items: center;font-size: 16px;">
                         <div style="flex-shrink:0">{{ item.name ? item.name : '' }}</div>
                          <span style="color: gray;padding-left: 10px;font-size: 14px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">{{ item.hospital_name }}</span>
                      </div>
                    </template>
                  </van-cell>
                  <van-cell >
                    <template #title >
                      <div style="display: flex;align-items: center;font-size: 14px;">
                         {{ '地区:' + (item.hospital_areas ?  item.hospital_areas : '暂无')}}
                          <span style="padding-left: 20px;font-size: 14px;">{{ '电话:' + item.mobile }}</span>
                      </div>
                    </template>
                  </van-cell>
                </div>
              </div>
              <van-divider style="margin:0px"/>
              <van-cell :value="item.created_at.split(' ')[0] + '邀请'"  is-link >
                <template #title >
                  <div style="display:flex;justify-content: space-between;color: #969799;">
                    <div style="display: flex;align-items: center;">
                      <van-icon name="edit" />
                      <div @click.stop="jumpToFlollow(item)">新增跟进</div>
                    </div>
                    <div style="display: flex;align-items: center;">
                      <van-icon name="award-o" />
                      <div @click.stop="jumpToTags(item)">新增标签</div>
                    </div>
                  </div>
                </template>
              </van-cell>
            </div>
            </van-list>
          </van-pull-refresh>
          <van-popup
            v-model="showPopupBeginDatePicker"
            position="bottom"
          >
            <van-datetime-picker
              v-model="begin_time"
              type="date"
              :formatter="formatter"
              :min-date="minDate"
              :max-date="maxDate"
              @confirm="confirmPopupBeginDatePicker"
              @cancel="showPopupBeginDatePicker = false"
            />
          </van-popup>
          <van-popup
            v-model="showPopupEndDatePicker"
            position="bottom"
          >
            <van-datetime-picker
              v-model="end_time"
              type="date"
              :formatter="formatter"
              :min-date="minDate"
              :max-date="maxDate"
              @confirm="confirmPopupEndDatePicker"
              @cancel="showPopupEndDatePicker = false"
            />
          </van-popup>
        </div>
      </van-tab>
      <van-tab title="拜访医生" name="c" >
        <div style="display: flex;align-items: center;">
          <van-search
            v-model="visitListQuery.keywords"
            placeholder="搜索医生"
            @search="onVisitSearch"
            style="width: 100%;"
          />
        </div>
        <div style="display: flex;justify-content: space-around;background-color: white;">
          <div style="display: flex;align-items: center;">
            <van-icon name="notes-o" />
            <div style="padding: 6px;font-size:14px;" @click="visitCalendarShow = true">选择时间</div>
          </div>
          <div style="display: flex;align-items: center;">
            <van-icon name="edit" />
            <div style="padding: 6px;font-size:14px;" @click="jumpToVisit()">新增拜访</div>
          </div>
          
        </div>
        <van-pull-refresh v-model="visitListLoading" @refresh="visitListOnRefresh" class="list-container">
          <van-list
            v-model="visitListLoading"
            :finished="visitListFinished"
            finished-text="没有更多了"
            @load="visitListOnLoad"
            offset="150"
          >
          <div v-for="(item, index) in promoterVisitList" :key="index" style="position: relative;padding-top:10px;border-radius:5px;overflow:hidden;">
            <div style="display: flex;text-align: center;margin: 6px;align-items: center;">
              <van-icon name="circle" size="12" color="#ff9000"/>
              <div style="font-size:14px;padding-left:4px">{{item.date}}</div>
            </div>
            <div style="position: absolute;top: 28px;left: 11px;height: 90%;background-color: lightgray;width: 1.5px;z-index: 0;"></div>
            <div v-for="(litem, lindex) in item.list" :key="lindex" style="position: relative;background-color: white;z-index: 2;margin:6px;padding:10px 8px;font-size:14px;">
              <div style="display: flex;justify-content: space-between;">
                <div style="display: flex;">
                  <div>{{litem.doctor_name}}</div>
                  <div style="color:gray;padding-left:10px">{{litem.doctor_mobile}}</div>
                </div>
                <van-tag round type="primary" :color="'#ff9000'">{{litem.promoter_visit_type_str}}</van-tag>
              </div>
              <div style="padding: 10px 0;color:gray">{{litem.content}}</div>
              <div style="display: flex;justify-content: space-between;color:gray">
                <div style="display: flex;">
                  <van-icon name="clock-o" />
                  <div>{{litem.created_at}}</div>
                </div>
                <div style="color:red;" @click="deletePromoterVisit(litem.id)">删除</div>
              </div>
            </div>
          </div>
          </van-list>
        </van-pull-refresh>
      </van-tab>
      <van-calendar v-model="visitCalendarShow" ref="visitCalendar" type="range" :min-date="minDate" :max-date="maxDate" color="#ff9000" title="拜访时间" @confirm="visitOnConfirm" >
        <template #footer>
          <div v-if="visitListQuery"></div>
          <div style="display: flex;flex-direction: row;justify-content: space-around;padding: 10px 0">
            <van-button round color="#ff9000" plain style="width: 150px" @click="visitReset">重置</van-button>
            <van-button round color="#ff9000" style="width: 150px" @click="visitOnConfirm($refs.visitCalendar.currentDate)">确认</van-button>
          </div>
        </template>
      </van-calendar>
    </van-tabs>
    
    <div style="height: 50px"></div>
    <router-view />
    <van-tabbar route active-color="#F7941E">
      <van-tabbar-item replace to="/home" icon="home-o">
        首页
      </van-tabbar-item>
      <van-tabbar-item v-if="info && info.level_id != 1" replace to="/doctors" icon="manager-o">
        医生
      </van-tabbar-item>
      <van-tabbar-item v-if="info && info.level_id != 4" replace to="/team" icon="friends-o">
        我的团队
      </van-tabbar-item>
      <van-tabbar-item replace to="/mine" icon="user-circle-o" dot >
        我的
      </van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script>
import { doctorInteractor, indexInteractor } from '@/core'
import AvatarButton from '@/components/AvatarButton'
import { readFromLocalStorage } from '@/core/services/cache'
import { Toast, Dialog } from 'vant'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn' // ES 2015 

dayjs.locale('zh-cn')
export default {
  name: 'Doctors',
  props: {},
  components: {
    AvatarButton
  },
  data() {
    return {
      info: undefined,
      list: [],
      loading: false,
      finished: false,
      refreshing: false,
      query: {
        page: 0,
        size: 10,
        status: -1,
        keywords: '',
        begin_time: undefined,
        end_time: undefined,
        credential_begin_time: undefined,
        credential_end_time: undefined,
        hospital_city: undefined,
      },
      begin_time: undefined,
      end_time: undefined,
      showPopup: false,
      minDate: new Date(2019, 0, 1),
      maxDate: new Date(),
      showPopupBeginDatePicker: false,
      showPopupEndDatePicker: false,
      option: [
        { text: '全部', value: -1 },
        { text: '未认证', value: 0 },
        { text: '已认证', value: 1 },
        { text: '拒绝', value: 2 },
      ],
      inviteCalendarShow: false,
      credentialCalendarShow: false,
      tabActive: undefined,
      visitListLoading: false,
      visitListFinished: false,
      visitListQuery: {
        keywords: '',
        begin_time: undefined,
        end_time: undefined,
        page: 0,
        size: 10,
      },
      visitCalendarShow: false,
      promoterVisitList: [],
      areasCalendarShow: false,
      areasValues: undefined,
      areasList:[],
      fieldNames: {
        text: 'name',
        value: 'id',
        children: 'children',
      },
    }
  },
  created() {
    let info = readFromLocalStorage('info')
    this.info = info
    this.$bus.$on("visitRefresh", (r) => {
      console.log('visitRefresh')
      this.visitListOnRefresh()
    });
    this.fetchAreaList()
  },
  mounted() {
   
  },
  methods: {

    fetchAreaList() {
      indexInteractor.fetchGetAreas({ level: 2 }).then(data => {
        this.areasList = data
      })
    },
    areasOnFinish(value, selectedOptions, tabIndex ) {
      this.areasCalendarShow = false;
      console.log(value);
      this.query.hospital_city = value.value;
      this.getDoctorsList(this.query)
    },
    areasCredentialReset() {
      this.areasCalendarShow = false;
      this.areasValues = undefined;
      this.query.hospital_city = undefined;
      this.getDoctorsList(this.query)
    },
    deletePromoterVisit(id) {
      Dialog.confirm({
        title: '温馨提醒',
        message: '是否删除拜访记录',
      })
        .then(() => {
          // on confirm
          doctorInteractor.fetchDeletePromoterVisit({visit_list_id: id}).then(data => {
            this.visitListOnRefresh()
          })
          
        })
        .catch(() => {
          // on cancel
        });
      
    },
    visitReset() {
      this.visitCalendarShow = false;
      this.$refs.visitCalendar.reset();
      this.visitListQuery.begin_time = undefined
      this.visitListQuery.end_time = undefined
      this.visitListQuery.page = 1
      this.promoterVisitList = []
      this.visitListFinished = false
      this.fetchGetPromoterVisitList(this.visitListQuery)
    },
    visitOnConfirm(date) {
      this.visitCalendarShow = false;
      this.visitListQuery.begin_time = date[0]
      this.visitListQuery.end_time = date[1]
      this.visitListQuery.page = 1
      this.promoterVisitList = []
      this.visitListFinished = false
      this.fetchGetPromoterVisitList(this.query)
    },
    statusChange() {
      this.query.page = 1
      this.list = []
      this.finished = false
      this.getDoctorsList(this.query)
    },
    inviteReset() {
      this.inviteCalendarShow = false;
      this.$refs.visitCalendar.reset();
      this.query.begin_time = undefined
      this.query.end_time = undefined
      this.query.page = 1
      this.list = []
      this.finished = false
      this.getDoctorsList(this.query)
    },
    inviteOnConfirm(date) {
      this.inviteCalendarShow = false;
      this.query.begin_time = date[0]
      this.query.end_time = date[1]
      this.query.page = 1
      this.list = []
      this.finished = false
      this.getDoctorsList(this.query)
    },
    credentialReset() {
      this.credentialCalendarShow = false;
      this.$refs.credentialCalendar.reset();
      this.query.credential_begin_time = undefined
      this.query.credential_end_time = undefined
      this.query.page = 1
      this.list = []
      this.getDoctorsList(this.query)
    },
    credentialOnConfirm(date) {
      this.credentialCalendarShow = false;
      this.query.credential_begin_time = date[0]
      this.query.credential_end_time = date[1]
      console.log(date)
      this.getDoctorsList(this.query)
    },
    formatter(type, val) {
      if (type === 'year') {
        return `${val}年`;
      } else if (type === 'month') {
        return `${val}月`
      } else {
        return `${val}日`
      }
      return val;
    },
    onSearch(k) {
      console.log(k)
      this.query.page = 1
      this.list = []
      this.getDoctorsList(this.query)
    },
    clickCell(item) {
      if (item.status == 0) {
        Toast.fail('该医生未认证')
      } else if (item.status == 2) {
        Toast.fail('该医生认证已拒绝')
      } else {
        this.$router.push({path:'/doctorDetail',query: { doctor_id : item.doctor_id}})
      }
      
    },
    jumpToVisit() {
      this.$router.push({path:'/visit'})
    },
    jumpToFlollow(item) {
      this.$router.push({path:'/follow',query: { doctor_id : item.doctor_id, doctor_name: item.name}})
    },
    jumpToTags(item) {
      this.$router.push({path:'/doctorTags',query: { doctor_id : item.doctor_id}})
    },
    
    confirmPopupBeginDatePicker(t) {
      this.query.begin_time = t
      this.showPopupBeginDatePicker = false
    },
    confirmPopupEndDatePicker(t) {
      this.query.end_time = t
      this.showPopupEndDatePicker = false
    },
    filterAction() {

      this.list = []
      this.showPopup = false
      this.finished = false
      this.query.page = 0
      
      // this.getDoctorsList(this.query)
    },
    cleanFilterAction() {
      this.end_time = undefined
      this.begin_time = undefined
      this.query.begin_time = undefined
      this.query.end_time = undefined
      this.showPopup = false
      this.list = []
      this.query = {
        page: 1,
        size: 10,
        status: '-1',
        keywords: this.query.keywords
      },
      this.getDoctorsList(this.query)
    },
    async getDoctorsList(query) {
      try {
        let q = Object.assign({}, query, query)
        if (q.begin_time) {
          q.begin_time = dayjs(query.begin_time).format('YYYY-MM-DD') 
        }
        if (q.end_time) {
          q.end_time = dayjs(query.end_time).format('YYYY-MM-DD') 
        }
        if (q.credential_begin_time) {
          q.credential_begin_time = dayjs(query.credential_begin_time).format('YYYY-MM-DD') 
        }
        if (q.credential_end_time) {
          q.credential_end_time = dayjs(query.credential_end_time).format('YYYY-MM-DD') 
        }

        await doctorInteractor.fetchDoctors(q).then(data => {
          if (this.query.page === 1) {
            this.list = data.doctors
          } else {
            this.list = [...this.list, ...data.doctors]
          }
          if (data.total <= this.list.length) {
            this.finished = true
          }
          console.log(this.list)
          this.loading = false
        })
        // this.listTotal = total
        
        
        
      } catch (error) {
        console.log(error)
      }
    },
    onLoad() {
      // 异步更新数据
      // setTimeout 仅做示例，真实场景中一般为 ajax 请求
      this.loading = true
      this.query.page = this.query.page + 1
      console.log('onLoad')
      this.getDoctorsList(this.query)
    },
    onRefresh() {
      // 清空列表数据
      this.finished = false
      console.log('onRefresh')
      console.log(this.query)
      // 重新加载数据
      // 将 loading 设置为 true，表示处于加载状态
      this.loading = true
      this.query.page = 1
      this.list = []
      this.getDoctorsList(this.query)
    },
    visitListOnLoad() {
      // 异步更新数据
      // setTimeout 仅做示例，真实场景中一般为 ajax 请求
      this.visitListLoading = true
      this.visitListQuery.page = this.visitListQuery.page + 1
      console.log('onLoad')
      this.fetchGetPromoterVisitList(this.visitListQuery)
    },
    visitListOnRefresh() {
      // 清空列表数据
      this.visitListFinished = false
      // 重新加载数据
      // 将 loading 设置为 true，表示处于加载状态
      this.visitListLoading = true
      this.visitListQuery.page = 1
      this.promoterVisitList = []
      this.fetchGetPromoterVisitList(this.visitListQuery)
    },
    onVisitSearch() {
      this.visitListQuery.page = 1
      this.promoterVisitList = []
      this.fetchGetPromoterVisitList()
    },
    async fetchGetPromoterVisitList(query) {
      try {
        let q = Object.assign({}, this.visitListQuery, query)
        if (this.visitListQuery.begin_time) {
          q.begin_time = dayjs(this.visitListQuery.begin_time).format('YYYY-MM-DD')
          q.end_time = dayjs(this.visitListQuery.end_time).format('YYYY-MM-DD')
        }
        const data = await doctorInteractor.fetchPromoterVisitList(q).then(data => {

          this.visitListLoading = false
          
          data.visit_list.forEach(element => {
            this.promoterVisitList.push(element)
          })
          
          let count = 0
          this.promoterVisitList.map(item => {
            count = count + item.list.length
          })

          if (count >= data.total) {
            this.visitListFinished = true
          } else {
            this.visitListFinished = false
          }

        })
      } catch (error) {
        console.log(error)
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.cell {
  background-color: white;
  margin: 10px;
  // height: 118px;
  border-radius: 10px;
  overflow: hidden;
}
</style>
