@import './transition.scss';
@import './_animation';

body {

  background-color: #F3F3F3;
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

#app {
  height: 100%;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fright {
  float: right;
}

.fleft {
  float: left;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

//main-container全局样式
.app-container {
  padding: 20px;
}

.text-center {
  text-align: center
}

.flyin-to-cart {
  animation: flyin 0.5s ease-in-out;
}
.unclickable {
  pointer-events: none;
}
.width-button {
  width: 100%;
  border: 0px;
  height: 54px;
}

.van-index-bar__sidebar {
  visibility: hidden !important;
}