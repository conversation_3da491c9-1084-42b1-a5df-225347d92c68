@mixin clearfix {
  &:after {
    content: "";
    display: table;
    clear: both;
  }
}

@mixin wh($width, $height) {
  width: $width;
  height: $height;
}

@mixin minwh($width, $height) {
  width: $width;
  height: $height;
}

@mixin maxwh($width, $height) {
  width: $width;
  height: $height;
}

@mixin scw($size, $color: #333, $weight: 400) {
  font-size: $size;
  color: $color;
  font-weight: $weight;
}

@mixin fixcc {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@mixin pcc {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

@mixin fcc() {
  display: flex;
  justify-content: center;
  align-items: center;
}

@mixin fh {
  display: flex;
  align-items: center;
}

@mixin fj($type: space-between) {
  display: flex;
  justify-content: $type;
}

@mixin relative {
  position: relative;
  width: 100%;
  height: 100%;
}

@mixin absolute($top:null, $right:null, $bottom:null, $left:null) {
  position: absolute;
  @if ($left!="" & & $left!=null) {
    left: $left;
  }
  @if ($right!="" & & $right!=null) {
    right: $right;
  }
  @if ($top!="" & & $top!=null) {
    top: $top;
  }
  @if ($bottom!="" & & $bottom!=null) {
    bottom: $bottom;
  }
}

@mixin flex($direction: column, $wrap: nowrap) {
  display: flex;
  flex-direction: $direction;
  flex-wrap: $wrap;
}

@mixin ellipsis($line: 2, $line-height: 1.2) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: $line;
  line-height: $line-height;
}


@mixin backgroundImage($url) {
	background-image: url($url);
	background-position: center;
	background-size: cover;
	background-repeat: no-repeat 0 0;
}
