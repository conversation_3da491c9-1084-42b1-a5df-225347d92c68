import Vue from 'vue'
import VueRouter from 'vue-router'

import Layout from '@/layout'
import componentsRouter from './modules/components'

Vue.use(VueRouter)

export const routes = [
  {
    path: '/login',
    component: () => import('@/pages/login/index'),
    name: 'Login',
    meta: { title: '登录' },
    hidden: true
  },
  {
    path: '/redirect',
    component: Layout,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/pages/redirect/index')
      }
    ]
  },
  {
    path: '',
    component: Layout,
    redirect: '/home',
    children: [
      {
        path: 'home',
        component: () => import('@/pages/home/<USER>'),
        name: 'Home',
        meta: { title: '首页', right_title: '消息', right_push: 'Messages' }
      },
      {
        path: 'qrcode',
        component: () => import('@/pages/qrcode/index'),
        name: 'Qrcode',
        meta: { title: '邀请医生' }
      },
      {
        path: 'inviteDoctor',
        component: () => import('@/pages/inviteDoctor/index'),
        name: 'InviteDoctor',
        meta: { title: '诚邀您加入喜郎中', hideNav: true  }
      },
      {
        path: 'partnerDetail',
        component: () => import('@/pages/partnerDetail/index'),
        name: 'partnerDetail'
      },
      {
        path: 'orderList',
        component: () => import('@/pages/orderList/index'),
        name: 'OrderList',
        meta: { title: '订单统计' }
      },
      {
        path: 'statisticsDoctorList',
        component: () => import('@/pages/statisticsDoctorList/index'),
        name: 'StatisticsDoctorList',
        meta: { title: '医生统计' }
      },
      {
        path: 'doctorActivities',
        component: () => import('@/pages/doctorActivities/index'),
        name: 'DoctorActivities',
        meta: { title: '医生活动列表' }
      },
      
    ]
  },
  {
    path: '',
    component: Layout,
    redirect: '/messages',
    children: [
      {
        path: 'messages',
        component: () => import('@/pages/messages/index'),
        name: 'Messages',
        meta: { title: '消息', right_title: '设置', right_push: 'MessageSettings' }
      },
      {
        path: 'messageSettings',
        component: () => import('@/pages/messages/messageSettings'),
        name: 'MessageSettings',
        meta: { title: '消息设置' }
      }
    ]
  },
  {
    path: '',
    component: Layout,
    redirect: '/forgot',
    children: [
      {
        path: 'forgot',
        component: () => import('@/pages/forgot/index'),
        name: 'Forgot',
        meta: { title: '忘记密码' }
      }
    ]
  },
  {
    path: '',
    component: Layout,
    redirect: '/team',
    children: [
      {
        path: 'team',
        component: () => import('@/pages/team/index'),
        name: 'Team',
        meta: { title: '我的团队', keepAlive: true  }
      },
      {
        path: 'teamMember',
        component: () => import('@/pages/teamMember/index'),
        name: 'TeamMember',
        meta: { title: '' }
      }
    ]
  },
  {
    path: '',
    component: Layout,
    redirect: '/pharmacyList',
    children: [
      {
        path: 'pharmacyList',
        component: () => import('@/pages/pharmacyList/index'),
        name: 'PharmacyList',
        meta: { title: '我的代理商', keepAlive: false  }
      },
      {
        path: 'pharmacyDetail',
        component: () => import('@/pages/pharmacyDetail/index'),
        name: 'pharmacyDetail'
      }
    ]
  },
  {
    path: '',
    component: Layout,
    redirect: '/doctors',
    children: [
      {
        path: 'doctors',
        component: () => import('@/pages/doctors/index'),
        name: 'Doctors',
        meta: { title: '医生', keepAlive: true, right_title: '医生排名', right_push: 'DoctorRank'  }
      },
      {
        path: 'doctorRank',
        component: () => import('@/pages/doctors/doctorRank'),
        name: 'DoctorRank',
        meta: { title: '排行榜', keepAlive: true, right_title: '提示', is_alert: true}
      },
      {
        path: 'follow',
        component: () => import('@/pages/doctors/follow'),
        name: 'Follow',
        meta: { title: '新增跟进'}
      },
      {
        path: 'visit',
        component: () => import('@/pages/doctors/visit'),
        name: 'Visit',
        meta: { title: '新增拜访', keepAlive: true,}
      },
      {
        path: 'visitDoctorList',
        component: () => import('@/pages/doctors/visitDoctorList'),
        name: 'VisitDoctorList',
        meta: { title: '选择医生'}
      },
      {
        path: 'visitDoctorEdit',
        component: () => import('@/pages/doctors/visitDoctorEdit'),
        name: 'visitDoctorEdit',
        meta: { title: '添加医生'}
      },
      
      
    ]
  },
  {
    path: '',
    component: Layout,
    redirect: '/doctorDetail/:id',
    children: [
      {
        path: 'doctorDetail',
        component: () => import('@/pages/doctorDetail/index'),
        name: 'DoctorDetail',
        meta: { title: '医生详情' }
      },
      {
        path: 'DoctorEditInfo',
        component: () => import('@/pages/doctorDetail/doctorEditInfo'),
        name: 'DoctorEditInfo',
        meta: { title: '医生详情' }
      },
      
      {
        path: 'doctorTags',
        component: () => import('@/pages/doctorDetail/doctorTags'),
        name: 'DoctorTags',
        meta: { title: '医生标签'}
      },
    ]
  },
  {
    path: '',
    component: Layout,
    redirect: '/targetDetail',
    children: [
      {
        path: 'targetDetail',
        component: () => import('@/pages/target/targetDetail'),
        name: 'TargetDetail',
        meta: { title: '业绩目标完成度' }
      }
    ]
  },
  {
    path: '',
    component: Layout,
    redirect: '/mine',
    children: [
      {
        path: 'mine',
        component: () => import('@/pages/mine/index'),
        name: 'Mine',
        meta: { title: '我的', keepAlive: true }
      },
      {
        path: 'doctorCareList',
        component: () => import('@/pages/doctorCareList/index'),
        name: 'DoctorCareList',
        meta: { title: '流失预警', is_alert: true, right_title: '提示', }
      },
      {
        path: 'versionList',
        component: () => import('@/pages/versionList/index'),
        name: 'VersionList',
        meta: { title: '喜郎中APP版本更新记录', keepAlive: true }
      },
      {
        path: 'mallList',
        component: () => import('@/pages/mall/mallList'),
        name: 'MallList',
        meta: { title: '积分商城', keepAlive: true }
      },
      {
        path: 'mallItemDetail/:id',
        name: 'MallItemDetail',
        component: () => import('@/pages/mall/mallItemDetail'),
        name: 'mallList',
        meta: { title: '商品详情', keepAlive: false }
      },
      {
        path: '/mallItemEdit',
        name: 'MallItemEdit',
        component: () => import('@/pages/mall/mallItemEdit'),
        meta: {
          title: '支付订单', keepAlive: false
        }
      },
      {
        path: '/mallItemBuySuccess',
        name: 'MallItemBuySuccess',
        component: () => import('@/pages/mall/mallItemBuySuccess'),
        meta: {
          title: '兑换成功'
        }
      },
      {
        path: '/mallExpress',
        name: 'MallExpress',
        component: () => import('@/pages/mall/mallExpress'),
        meta: {
          title: '物流详情'
        }
      },
      {
        path: '/mallAddressEdit',
        name: 'MallAddressEdit',
        component: () => import('@/pages/mall/mallAddressEdit'),
        meta: {
          title: '编辑地址'
        }
      },
      {
        path: '/points',
        name: 'Points',
        component: () => import('@/pages/points/index'),
        meta: {
          title: '个人积分', right_title:'规则', right_push: 'points_web'
        }
      },
      {
        path: '/pointsDetailList',
        name: 'PointsDetailList',
        component: () => import('@/pages/points/pointsDetailList'),
        meta: {
          title: '积分明细'
        }
      },
      {
        path: '/myEarnings',
        name: 'MyEarnings',
        component: () => import('@/pages/myEarnings/index'),
        meta: {
          title: '我的收益'
        }
      },
      {
        path: '/incomeDetailList',
        name: 'IncomeDetailList',
        component: () => import('@/pages/myEarnings/income_detail_list'),
        meta: {
          title: '收益明细'
        }
      },
      {
        path: '/editPromoterInfo',
        name: 'EditPromoterInfo',
        component: () => import('@/pages/myEarnings/editPromoterInfo'),
        meta: {
          title: '完善资料'
        }
      },
      
      
    ]
  },
  componentsRouter
]

const createRouter = () => new VueRouter({
  mode: 'history',
  scrollBehavior: () => ({ y: 0 }),
  routes
})

const router = createRouter()

export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher
}

export default router
