<template>
  <div style="display: flex;
    flex-direction: column;
    justify-content: center;
    height: 60px;
    align-items: center;
    background-color: #F9F9F9;
    border-radius: 5px;">
    <div style="font-size: 11px;color: #8C8C8C;">{{title}}</div>
    <div v-if="underline" style="font-size: 15px;font-weight: bold;padding-top: 4px; text-decoration: underline;">{{count}}</div>
    <div v-else style="font-size: 15px;font-weight: bold;padding-top: 4px; text-decoration: none;">{{count}}</div>
  </div>
</template>

<script>
export default {
  name: 'DoctorStatisticsTitle',
  props: {
    title: {
      type: String,
      value: '',
    },
    count: {
      type: String,
      value: '',
    },
    underline: {
      type: Boolean,
      value: true,
    }
  },
  data() {
    return {}
  },
  created() {},
  mounted() {},
  methods: {}
}
</script>

<style lang="scss" scoped>
</style>
