<template>
  <div style="display: flex;padding: 8px;">
    <div style="background-color: #F7941E; width: 4px;" ></div>
    <div style="margin-left:6px; font-size:15px">{{title}}</div>
  </div>
</template>

<script>
export default {
  name: 'CommonHeader',
  props: {
    title: {
      type: String,
      value: '',
    },
  },
  data() {
    return {}
  },
  created() {},
  mounted() {},
  methods: {}
}
</script>

<style lang="scss" scoped>
</style>
