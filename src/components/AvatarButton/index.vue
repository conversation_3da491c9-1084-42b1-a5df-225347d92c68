<template>
  <div class="content">
    <div class="img-content">
      <van-image
        class="img"
        round
        width="52px"
        height="52px"
        :src="url"
      />
    </div>
    <div class="bottom-button">
      <van-tag v-if="status != 99" :color="status | statusFilter" round>{{status | statusStringFilter}}</van-tag>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AvatarButton',
  props: {
    url: {
      type: String,
      value: '',
    },
    title: {
      type: String,
      value: '',
    },
    status: {
      type: Number,
      value: '',
    },
    statusString: {
      type: String,
      value: '',
    },
  },
  filters: {
    statusFilter(status) {
      const statusMap = {
        0: '#B0B0B0',
        1: '#f6d21c',
        2: '#FF4723',
        3: '#B0B0B0',
      }
      return statusMap[status]
    },
    statusStringFilter(status) {
      const statusMap = {
        0: '未认证',
        1: '已认证',
        2: '已拒绝',
        3: '停用',
      }
      return statusMap[status]
    },
  },
  data() {
    return {
      imgPath: ''
    }
  },
  created() {
    this.imgPath = process.env.VUE_APP_IMG_BASE_API
  },
  mounted() {},
  methods: {}
}
</script>

<style lang="scss" scoped>
.content {
  width: 68px;
}

.img-content {
  padding:  7px;
  position: relative;
  z-index: 0;
}
.img {
  width: 52px;
  height: 52px;
  position: absolute;
  z-index: 2;
}
.bottom-button {
  position: relative;
  z-index: 1;
  margin-top: 30px;
  text-align: center;
  // padding: 0 10px;
}
</style>
