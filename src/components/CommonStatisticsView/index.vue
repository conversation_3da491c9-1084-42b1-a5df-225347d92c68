<template>
  <div>
    <div class="mid-card" style="margin-top: 20px" >
      <div style="display: flex;justify-content: space-between;">
        <div style="display: flex;align-items: center;">
          <CommonHeader style="padding: 15px 10px" :title="headerTitle ? headerTitle : '业绩数据'"/>
        </div>
        <div v-if="isShowTimePicker === 1" style="display: flex;
    align-items: center;">
          <div style="display: flex;align-items: center;margin-right: 10px;" @click="showPopup = true">
            <div style="font-size: 10px; color: gray" >{{begin_time.substring(0,10) + ' 至 ' + end_time.substring(0,10)}}<span style="color: #ff9000"> 筛选</span></div>
            <!-- <van-dropdown-menu>
              <van-dropdown-item v-model="timePicker" :options="timeOption" />
            </van-dropdown-menu> -->
          </div>
        </div>
      </div>
      <van-row type="flex" justify="space-around" v-if="counts" >
        <van-col span="11" @click="tapOrderAction"><DoctorStatisticsTitle :underline="underline" title="订单笔数" :count="counts.orders.order_number.toString()"/></van-col>
        <van-col span="11" @click="tapOrderAction"><DoctorStatisticsTitle :underline="underline" title="订单金额" :count="counts.orders.order_total_amount.toString()"/></van-col>
      </van-row>
      <div v-if="info && info.promoter_type == 1 && viewType == 0">
        <van-cell style="margin-top:10px" title="药费金额" value-class="cell-value" :value="'¥' + counts.orders.base_medicine_charge" v-if="counts"/>
        <van-cell title="纯销金额" value-class="cell-value" :value="'¥' + counts.orders.order_amount" v-if="counts"/>
        <van-cell title="奖金预估" value-class="cell-value" :value="'¥' + counts.orders.bonus_predict" v-if="counts"/>
      </div>
      <div style="height: 14px"></div>
    </div>
    <div class="mid-card" style="height: 132px; margin-top: 20px">
      <CommonHeader style="padding: 15px 10px" :title="bottomTitle ? bottomTitle : '医生数据看板'" />
      <van-row type="flex" justify="space-around" v-if="counts">
        <van-col span="5" @click="tapDoctorAction(4)"><DoctorStatisticsTitle :underline="underline" title="邀请医生数" :count="counts.doctors.doctors.toString()"/></van-col>
        <van-col span="5" @click="tapDoctorAction(1)"><DoctorStatisticsTitle :underline="underline" title="已认证医生" :count="counts.doctors.credentials.toString()"/></van-col>
        <van-col span="5" @click="tapDoctorAction(3)"><DoctorStatisticsTitle :underline="underline" title="未认证医生" :count="counts.doctors.not_credentials.toString()"/></van-col>
        <van-col span="6" @click="tapDoctorAction(2)"><DoctorStatisticsTitle :underline="underline" title="认证拒绝医生" :count="counts.doctors.refuse_credentials.toString()"/></van-col>
      </van-row>
    </div>
    <van-popup
        v-model="showPopup"
        position="center"
        :close-on-click-overlay = false
        >
        <van-form>
          <van-field
            readonly
            clickable
            name="picker"
            :value="query.begin_time ? query.begin_time.toLocaleDateString() : ''"
            label="开始时间"
            placeholder="点击选择开始时间"
            @click="showPopupBeginDatePicker = true"
          />
          <van-field
            readonly
            clickable
            name="picker2"
            :value="query.end_time ? query.end_time.toLocaleDateString() : ''"
            label="结束时间"
            placeholder="点击选择结束时间"
            @click="showPopupEndDatePicker = true"
          />
        </van-form>
        <div>
          <van-row>
            <van-col span="24">
              <van-button class="width-button" color="#F7941E" type="primary" @click="dateIsPicked">确定</van-button>
            </van-col>
          </van-row>
        </div>
      </van-popup>
      <van-popup
        v-model="showPopupBeginDatePicker"
        position="bottom"
      >
        <van-datetime-picker
          v-model="popup.begin_time"
          type="date"
          :formatter="formatter"
          :min-date="minDate"
          :max-date="maxDate"
          @confirm="confirmPopupBeginDatePicker"
          @cancel="showPopupBeginDatePicker = false"
        />
      </van-popup>
      <van-popup
        v-model="showPopupEndDatePicker"
        position="bottom"
      >
        <van-datetime-picker
          v-model="popup.end_time"
          type="date"
          :formatter="formatter"
          :min-date="minDate"
          :max-date="maxDate"
          @confirm="confirmPopupEndDatePicker"
          @cancel="showPopupEndDatePicker = false"
        />
      </van-popup>
  </div>
</template>

<script>
import CommonHeader from '@/components/CommonHeader'
import DoctorStatisticsTitle from '@/components/DoctorStatisticsTitle'
import dayjs from 'dayjs'
import quarterOfYear from 'dayjs/plugin/quarterOfYear' // ES 2015
import 'dayjs/locale/zh-cn' // ES 2015 

dayjs.locale('zh-cn')
dayjs.extend(quarterOfYear)
export default {
  name: 'CommonStatisticsView',
  components: {
    CommonHeader, DoctorStatisticsTitle
  },
  props: {
    info: {
      type: Object,
      value: '',
    },
    viewType: {
      type: Number,
      value: 0,
    },
    isShowTimePicker: {
      type: Number,
      value: 1,
    },
    headerTitle: {
      type: String,
      value: undefined,
    },
    bottomTitle: {
      type: String,
      value: undefined,
    },
    counts: {
      type: Object,
      value: '',
    },
    begin_time: {
      type: String,
      value: '',
    },
    end_time: {
      type: String,
      value: '',
    },
    sourceType: {
      type: Number,
      value: 1,
    },
    level: {
      type: Number,
      value: 1,
    },
    underline: {
      type: Boolean,
      value: true,
    }
  },
  data() {
    return {
      timePicker: 0,
      timeOption: [
        { text: '本月', value: 0 },
        { text: '上个月', value: 6 },
        { text: '本日', value: 1 },
        { text: '本周', value: 2 },
        { text: '本季度', value: 3 },
        { text: '上季度', value: 4 },
        { text: '本年度', value: 5 },
      ],
      query: {
        begin_time: undefined,
        end_time: undefined
      },
      popup: {
        begin_time: undefined,
        end_time: undefined
      },
      show: false,
      showPopup: false,
      minDate: new Date(2019, 0, 1),
      maxDate: new Date(),
      showPopupBeginDatePicker: false,
      showPopupEndDatePicker: false,
    }
  },
  watch: {
        timePicker: function (val, oldVal) {
          console.log('new a: %s, old a: %s', val, oldVal)
          let b,e
          let now = new Date
          switch (val) {
              case 0:
                  b = new Date(now.getFullYear(), now.getMonth(), 1)
                  e = now
                  break;
              case 1:
                  b = now
                  e = now
                  break;
              case 2:
                  let w = dayjs(now).day(1)
                  b = w.$d
                  e = now
                  console.log(w)
                  break;
              case 3:
                  let q = dayjs(now).quarter()
                  let d = dayjs(now.getFullYear().toString()).quarter(q)
                  b = d.$d
                  e = now
                  break;
              case 4:
                  let qq = dayjs(now).quarter()
                  let dd = dayjs(now.getFullYear().toString()).quarter(qq - 1)
                  let ww = dayjs(now.getFullYear().toString()).quarter(qq - 1).endOf('quarter')
                  b = dd.$d
                  e = ww.$d
                  console.log(dayjs(e).format('YYYY-MM-DD'))
                  break;
              case 5:
                  b = new Date(now.getFullYear(),0,1,0,0,0)
                  e = now
                  break;
              case 6:
                  b = new Date(now.getFullYear(), now.getMonth() - 1, 1)
                  e = dayjs(b).date(dayjs(b).daysInMonth())
                  break;
          }
          this.query.begin_time = dayjs(b).format('YYYY-MM-DD') 
          this.query.end_time = dayjs(e).format('YYYY-MM-DD')
          this.$emit('timePickerAction', this.query)
        },
        begin_time: function (val, oldVal) {
          console.log(val)
          this.popup.begin_time = dayjs(this.begin_time).toDate()
          this.query.begin_time = dayjs(this.begin_time).toDate()
        },
        end_time: function (val, oldVal) {
          this.popup.end_time = dayjs(this.end_time).toDate()
          this.query.end_time = dayjs(this.end_time).toDate()
        }
  },
  created() {
    this.popup.begin_time = dayjs(this.begin_time).toDate()
    this.popup.end_time = dayjs(this.end_time).toDate()
    this.query.begin_time = dayjs(this.begin_time).toDate()
    this.query.end_time = dayjs(this.end_time).toDate()
  },
  mounted() {},
  methods: {
    formatter(type, val) {
      if (type === 'year') {
        return `${val}年`;
      } else if (type === 'month') {
        return `${val}月`
      } else {
        return `${val}日`
      }
      return val;
    },
    confirmPopupEndDatePicker(v) {
      console.log(v)
      this.query.end_time = v
      this.showPopupEndDatePicker = false
    },
    confirmPopupBeginDatePicker(v) {
      console.log(v)
      this.query.begin_time = v
      this.showPopupBeginDatePicker = false
    },
    dateIsPicked() {
      this.showPopup = false
      let dict = {
        begin_time: dayjs(this.query.begin_time).format('YYYY-MM-DD'),
        end_time: dayjs(this.query.end_time).format('YYYY-MM-DD')
      }
      this.$emit('timePickerAction', dict)
    },
    tapDoctorAction(s) {
      let dict = {
        begin_time: dayjs(this.query.begin_time).format('YYYY-MM-DD'),
        end_time: dayjs(this.query.end_time).format('YYYY-MM-DD'),
        type: this.sourceType,
        status: s
      }
      if (this.level) {
        dict['level'] = this.level
      }
      console.log(dict)
      this.$emit('tapDoctorAction', dict)
    },
    tapOrderAction() {
      let dict = {
        begin_time: dayjs(this.query.begin_time).format('YYYY-MM-DD'),
        end_time: dayjs(this.query.end_time).format('YYYY-MM-DD'),
        type: this.sourceType,
      }
      if (this.level) {
        dict['level'] = this.level
      }
      this.$emit('tapOrderAction', dict)
    }
  }
}
</script>

<style lang="scss" scoped>
.mid-card {
  margin: 0px -6% 0px 6%;
  width: 88%;
  background-color: white;
  border-radius: 5px;
}
</style>
