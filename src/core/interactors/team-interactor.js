import { Request } from '@/core/services/http/request'
import { Team, TeamPromoterStatistics } from '@/constants/api/team'

class TeamHttpInteractor {
  service
  constructor(service) {
    this.service = service
  }
  async getTeamPromoterStatistics(data) {
    try {
      const options = { url: TeamPromoterStatistics, data }
      return await this.service.post(options)
    } catch (error) {
      throw error
    }
  }
  async getTeam(data) {
    try {
      const options = { url: Team, data }
      return await this.service.post(options)
    } catch (error) {
      throw error
    }
  }
  async createTeam(data) {
    try {
      const optons = { url: Team, data }
      await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async deleteTeam(id) {
    try {
      const options = { url: `$Team/${id}` }
      await this.service.delete(options)
    } catch (error) {
      throw error
    }
  }
  async updateTeam(data, id) {
    try {
      const optons = {
        url: `$Team/${id}`, data }
      await this.service.put(optons)
    } catch (error) {
      throw error
    }
  }
}

const teamHttpInteractor = new TeamHttpInteractor(Request.getInstance())
export default teamHttpInteractor
