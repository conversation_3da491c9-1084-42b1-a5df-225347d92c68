import { Request } from '@/core/services/http/request'
import { EditExtraDoctorInfo, GetExtraDoctorInfo, EditDoctorBirthday, DeletePromoterVisit, SetPromoterVisit, SetPromoterVisitDoctor, GetPromoterVisitDoctorList, GetPromoterVisitTypeList, GetPromoterVisitList, SetPromoterTag, GetPromoterTag, GetPromoterDoctorTag, SetPromoterDoctorTag, DeletePromoterFollow, GetPromoterFollowList, SetPromoterFollow, PromoterFollowType, <PERSON>, Doctor<PERSON>n<PERSON>, DoctorReportList } from '@/constants/api/doctor'

class DoctorHttpInteractor {
  service
  constructor(service) {
    this.service = service
  }
  async fetchEditExtraDoctorInfo(params) {
    try {
      const optons = { url: EditExtraDoctorInfo, params }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchGetExtraDoctorInfo(params) {
    try {
      const optons = { url: GetExtraDoctorInfo, params }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchEditDoctorBirthday(params) {
    try {
      const optons = { url: EditDoctorBirthday, params }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchDeletePromoterVisit(params) {
    try {
      const optons = { url: DeletePromoterVisit, params }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchSetPromoterVisit(params) {
    try {
      const optons = { url: SetPromoterVisit, params }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchSetPromoterVisitDoctor(params) {
    try {
      const optons = { url: SetPromoterVisitDoctor, params }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchPromoterVisitDoctorList(params) {
    try {
      const optons = { url: GetPromoterVisitDoctorList, params }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchPromoterVisitTypeList(params) {
    try {
      const optons = { url: GetPromoterVisitTypeList, params }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchPromoterVisitList(params) {
    try {
      const optons = { url: GetPromoterVisitList, params }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchSetPromoterTag(params) {
    try {
      const optons = { url: SetPromoterTag, params }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchGetPromoterTag(params) {
    try {
      const optons = { url: GetPromoterTag, params }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchGetPromoterDoctorTag(params) {
    try {
      const optons = { url: GetPromoterDoctorTag, params }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchSetPromoterDoctorTag(params) {
    try {
      const optons = { url: SetPromoterDoctorTag, params }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }

  async deletePromoterFollow(params) {
    try {
      const optons = { url: DeletePromoterFollow, params }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchGetPromoterFollowList(params) {
    try {
      const optons = { url: GetPromoterFollowList, params }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchSetPromoterFollow(params) {
    try {
      const optons = { url: SetPromoterFollow, params }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchPromoterFollowType(params) {
    try {
      const optons = { url: PromoterFollowType, params }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchDoctorReportList(params) {
    try {
      const optons = { url: DoctorReportList, params }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchDoctorInfo(params) {
    try {
      const optons = { url: DoctorInfo, params }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchDoctors(params) {
    try {
      const optons = { url: Doctor, params }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async getDoctor() {
    try {
      const options = { url: Doctor }
      return await this.service.get(options)
    } catch (error) {
      throw error
    }
  }
  async createDoctor(data) {
    try {
      const optons = { url: Doctor, data }
      await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async deleteDoctor(id) {
    try {
      const options = { url: `$Doctor/${id}` }
      await this.service.delete(options)
    } catch (error) {
      throw error
    }
  }
  async updateDoctor(data, id) {
    try {
      const optons = {
        url: `$Doctor/${id}`, data }
      await this.service.put(optons)
    } catch (error) {
      throw error
    }
  }
}

const doctorHttpInteractor = new DoctorHttpInteractor(Request.getInstance())
export default doctorHttpInteractor
