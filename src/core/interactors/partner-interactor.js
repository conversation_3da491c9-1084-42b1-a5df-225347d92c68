import { Request } from '@/core/services/http/request'
import { PartnerTeam, PartnerSalesReport, PromoterPartners, PromoterPartnersIndex, PromoterPartnersAccountInfo, PromoterPartnersIncomeDetail } from '@/constants/api/partner'

class PartnerHttpInteractor {
  service
  constructor(service) {
    this.service = service
  }
  async fetchPromoterPartnersAccountInfo(data) {
    try {
      const options = { url: PromoterPartnersAccountInfo, data }
      return await this.service.post(options)
    } catch (error) {
      throw error
    }
  }
  async fetchPromoterPartnersIncomeDetail(data) {
    try {
      const options = { url: PromoterPartnersIncomeDetail, data }
      return await this.service.post(options)
    } catch (error) {
      throw error
    }
  }
  async fetchPromoterPartnersIndex(data) {
    try {
      const options = { url: PromoterPartnersIndex, data }
      return await this.service.post(options)
    } catch (error) {
      throw error
    }
  }
  
  async fetchPartnerTeam(data) {
    try {
      const options = { url: PartnerTeam, data }
      return await this.service.post(options)
    } catch (error) {
      throw error
    }
  }
  async fetchPartnerSalesReport(data) {
    try {
      const options = { url: PartnerSalesReport, data }
      return await this.service.post(options)
    } catch (error) {
      throw error
    }
  }
  async fetchPromoterPartners(data) {
    try {
      const options = { url: PromoterPartners, data }
      return await this.service.post(options)
    } catch (error) {
      throw error
    }
  }
  async getPartner() {
    try {
      const options = { url: Partner }
      return await this.service.get(options)
    } catch (error) {
      throw error
    }
  }
  async createPartner(data) {
    try {
      const optons = { url: Partner, data }
      await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async deletePartner(id) {
    try {
      const options = { url: `$Partner/${id}` }
      await this.service.delete(options)
    } catch (error) {
      throw error
    }
  }
  async updatePartner(data, id) {
    try {
      const optons = {
        url: `$Partner/${id}`, data }
      await this.service.put(optons)
    } catch (error) {
      throw error
    }
  }
}

const partnerHttpInteractor = new PartnerHttpInteractor(Request.getInstance())
export default partnerHttpInteractor
