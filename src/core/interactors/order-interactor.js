import { Request } from '@/core/services/http/request'
import { Order, OrderReportList } from '@/constants/api/order'

class OrderHttpInteractor {
  service
  constructor(service) {
    this.service = service
  }
  async fetchOrderReportList(params) {
    try {
      const optons = { url: OrderReportList, params }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async getOrder() {
    try {
      const options = { url: Order }
      return await this.service.get(options)
    } catch (error) {
      throw error
    }
  }
  async createOrder(data) {
    try {
      const optons = { url: Order, data }
      await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async deleteOrder(id) {
    try {
      const options = { url: `$Order/${id}` }
      await this.service.delete(options)
    } catch (error) {
      throw error
    }
  }
  async updateOrder(data, id) {
    try {
      const optons = {
        url: `$Order/${id}`, data }
      await this.service.put(optons)
    } catch (error) {
      throw error
    }
  }
}

const orderHttpInteractor = new OrderHttpInteractor(Request.getInstance())
export default orderHttpInteractor
