import { Request } from '@/core/services/http/request'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, DoctorCareNotice, GetAreas, TargetList, IndexCRM, SystemMessageRead, UpdateAnnouncement, PromoterDoctorRank, PromoterDoctorActivities, SystemMessageConfigList, SystemMessageConfigSave, SystemMessageList, VersionList, Index, PromoterInfo, PartnerPeport, AgentIndex } from '@/constants/api/index'

class IndexHttpInteractor {
  service
  constructor(service) {
    this.service = service
  }
  async fetchDoctorCareList(params) {
    try {
      const options = { url: DoctorCareList, params }
      return await this.service.post(options)
    } catch (error) {
      throw error
    }
  }
  async fetchDoctorCareNotice(params) {
    try {
      const options = { url: DoctorCareNotice, params }
      return await this.service.post(options)
    } catch (error) {
      throw error
    }
  }
  async fetchGetAreas(params) {
    try {
      const options = { url: GetAreas, params }
      return await this.service.post(options)
    } catch (error) {
      throw error
    }
  }
  async fetchTargetList(params) {
    try {
      const options = { url: TargetList, params }
      return await this.service.post(options)
    } catch (error) {
      throw error
    }
  }
  async fetchIndexCRM(params) {
    try {
      const options = { url: IndexCRM, params }
      return await this.service.post(options)
    } catch (error) {
      throw error
    }
  }
  async fetchSystemMessageRead(params) {
    try {
      const options = { url: SystemMessageRead, params }
      return await this.service.post(options)
    } catch (error) {
      throw error
    }
  }
  async fetchUpdateAnnouncement(params) {
    try {
      const options = { url: UpdateAnnouncement, params }
      return await this.service.post(options)
    } catch (error) {
      throw error
    }
  }
  async fetchPromoterDoctorRank(params) {
    try {
      const options = { url: PromoterDoctorRank, params }
      return await this.service.post(options)
    } catch (error) {
      throw error
    }
  }
  async fetchPromoterDoctorActivities(params) {
    try {
      const options = { url: PromoterDoctorActivities, params }
      return await this.service.post(options)
    } catch (error) {
      throw error
    }
  }
  async systemMessageConfigList(params) {
    try {
      const options = { url: SystemMessageConfigList, params }
      return await this.service.post(options)
    } catch (error) {
      throw error
    }
  }
  async systemMessageConfigSave(params) {
    try {
      const options = { url: SystemMessageConfigSave, params }
      return await this.service.post(options)
    } catch (error) {
      throw error
    }
  }
  async getSystemMessageList(params) {
    try {
      const options = { url: SystemMessageList, params }
      return await this.service.post(options)
    } catch (error) {
      throw error
    }
  }
  async getVersionList(params) {
    try {
      const options = { url: VersionList, params }
      return await this.service.post(options)
    } catch (error) {
      throw error
    }
  }
  async getAgentIndex(params) {
    try {
      const options = { url: AgentIndex, params }
      return await this.service.post(options)
    } catch (error) {
      throw error
    }
  }
  async getPartnerPeport(params) {
    try {
      const options = { url: PartnerPeport, params }
      return await this.service.post(options)
    } catch (error) {
      throw error
    }
  }
  async getPromoterInfo() {
    try {
      const options = { url: PromoterInfo }
      return await this.service.post(options)
    } catch (error) {
      throw error
    }
  }
  async getIndex(params) {
    try {
      const options = { url: Index, params }
      return await this.service.post(options)
    } catch (error) {
      throw error
    }
  }
  async createIndex(data) {
    try {
      const optons = { url: Index, data }
      await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async deleteIndex(id) {
    try {
      const options = { url: `$Index/${id}` }
      await this.service.delete(options)
    } catch (error) {
      throw error
    }
  }
  async updateIndex(data, id) {
    try {
      const optons = {
        url: `$Index/${id}`, data }
      await this.service.put(optons)
    } catch (error) {
      throw error
    }
  }
}

const indexHttpInteractor = new IndexHttpInteractor(Request.getInstance())
export default indexHttpInteractor
