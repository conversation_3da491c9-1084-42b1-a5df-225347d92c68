import { Request } from '@/core/services/http/request'
import { Pharmacy, PharmacyInfo, PharmacyDoctorList } from '@/constants/api/pharmacy'

class PharmacyHttpInteractor {
  service
  constructor(service) {
    this.service = service
  }
  async fetchPharmacyDoctorList(data) {
    try {
      const options = { url: PharmacyDoctorList, data }
      return await this.service.post(options)
    } catch (error) {
      throw error
    }
  }
  async fetchPharmacyInfo(data) {
    try {
      const options = { url: PharmacyInfo, data }
      return await this.service.post(options)
    } catch (error) {
      throw error
    }
  }
  async fetchPharmacyList(data) {
    try {
      const options = { url: Pharmacy, data }
      return await this.service.post(options)
    } catch (error) {
      throw error
    }
  }
  async getPharmacy() {
    try {
      const options = { url: Pharmacy }
      return await this.service.get(options)
    } catch (error) {
      throw error
    }
  }
  async createPharmacy(data) {
    try {
      const optons = { url: Pharmacy, data }
      await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async deletePharmacy(id) {
    try {
      const options = { url: `$Pharmacy/${id}` }
      await this.service.delete(options)
    } catch (error) {
      throw error
    }
  }
  async updatePharmacy(data, id) {
    try {
      const optons = {
        url: `$Pharmacy/${id}`, data }
      await this.service.put(optons)
    } catch (error) {
      throw error
    }
  }
}

const pharmacyHttpInteractor = new PharmacyHttpInteractor(Request.getInstance())
export default pharmacyHttpInteractor
