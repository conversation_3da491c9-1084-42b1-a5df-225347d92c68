import { Request } from '@/core/services/http/request'
import { PointLogs, PointIndex, ActivityLKMultipleJan, ActivityLKReceiveJan, ActivityLKDetailJan, ActivityDoctorReportURL, ActivityDoctorReport2022, ActivityDoctorReport2022Share, ActivityConfigReceive, ActivityConfigDetail, ActivitySubsidiesDetail, ActivityNationalDetail, ActivitySettledReceive, ActivitySettledDetail, ActivityTargetDetail, ActivityInviteDetail, ActivityInviteDetailCash, ActivityInviteIndex, ActivityLKReceive, ActivityLKDetail, ActivityXTReceive, ActivityXTDetail, GetImgConfig, MallCheckCoupons, ActivityGBDetail, ActivityReceive, ActivityReturn, MallStoreCategories, MallStoreList, MallStoreActivityDetail, MallStoreActivities, MallDoctorCards, MallDoctorCardDetail, MallGetLogisticsTracking, MallGetIntegralOrderList, MallCreateAddress, MallGetAllAreas, MallAddressList, MallCreateOrder, MallGoodsInfo, MallBuyGoods, MallIntegralInfo, MallStoreSlides, MallStoreIndex } from '@/constants/api/mall'

class MallHttpInteractor {
  service
  constructor(service) {
    this.service = service
  }

  async fetchPointLogs(data) {
    try {
      const optons = { url: PointLogs, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }

  async fetchPointIndex(data) {
    try {
      const optons = { url: PointIndex, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }

  async fetchActivityLKMultipleJan(data) {
    try {
      const optons = { url: ActivityLKMultipleJan, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }

  async fetchActivityLKReceiveJan(data) {
    try {
      const optons = { url: ActivityLKReceiveJan, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }

  async fetchActivityLKDetailJan(data) {
    try {
      const optons = { url: ActivityLKDetailJan, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }

  async fetchActivityDoctorReportURL(data) {
    try {
      const optons = { url: ActivityDoctorReportURL, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }

  async fetchActivityDoctorReport2022Share(data) {
    try {
      const optons = { url: ActivityDoctorReport2022Share, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchActivityDoctorReport2022(data) {
    try {
      const optons = { url: ActivityDoctorReport2022, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchActivityConfigReceive(data) {
    try {
      const optons = { url: ActivityConfigReceive, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchActivityConfigDetail(data) {
    try {
      const optons = { url: ActivityConfigDetail, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchActivitySubsidiesDetail(data) {
    try {
      const optons = { url: ActivitySubsidiesDetail, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchActivityNationalDetail(data) {
    try {
      const optons = { url: ActivityNationalDetail, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchActivitySettledReceive(data) {
    try {
      const optons = { url: ActivitySettledReceive, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchActivitySettledDetail(data) {
    try {
      const optons = { url: ActivitySettledDetail, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchActivityTargetDetail(data) {
    try {
      const optons = { url: ActivityTargetDetail, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchActivityInviteDetailCash(data) {
    try {
      const optons = { url: ActivityInviteDetailCash, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchActivityInviteDetail(data) {
    try {
      const optons = { url: ActivityInviteDetail, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchActivityInviteIndex(data) {
    try {
      const optons = { url: ActivityInviteIndex, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchActivityLKReceive(data) {
    try {
      const optons = { url: ActivityLKReceive, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchActivityLKDetail(data) {
    try {
      const optons = { url: ActivityLKDetail, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchActivityXTReceive(data) {
    try {
      const optons = { url: ActivityXTReceive, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchActivityXTDetail(data) {
    try {
      const optons = { url: ActivityXTDetail, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchGetImgConfig(data) {
    try {
      const optons = { url: GetImgConfig, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchMallCheckCoupons(data) {
    try {
      const optons = { url: MallCheckCoupons, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchActivityGBDetail(data) {
    try {
      const optons = { url: ActivityGBDetail, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchActivityReceive(data) {
    try {
      const optons = { url: ActivityReceive, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchActivityReturn(data) {
    try {
      const optons = { url: ActivityReturn, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchMallStoreCategories(data) {
    try {
      const optons = { url: MallStoreCategories, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchMallStoreList(data) {
    try {
      const optons = { url: MallStoreList, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchMallStoreActivityDetail(data) {
    try {
      const optons = { url: MallStoreActivityDetail, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchMallStoreActivities(data) {
    try {
      const optons = { url: MallStoreActivities, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchMallDoctorCards(data) {
    try {
      const optons = { url: MallDoctorCards, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchMallDoctorCardsDetail(data) {
    try {
      const optons = { url: MallDoctorCardDetail, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchMallGetLogisticsTracking(data) {
    try {
      const optons = { url: MallGetLogisticsTracking, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchMallIntegralOrderList(data) {
    try {
      const optons = { url: MallGetIntegralOrderList, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchMallCreateAddress(data) {
    try {
      const optons = { url: MallCreateAddress, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchMallGetAllAreas(data) {
    try {
      const optons = { url: MallGetAllAreas, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchMallCreateOrder(data) {
    try {
      const optons = { url: MallCreateOrder, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchMallAddressList(data) {
    try {
      const optons = { url: MallAddressList, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchMallGoodsInfo(data) {
    try {
      const optons = { url: MallGoodsInfo, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchMallBuyGoods(data) {
    try {
      const optons = { url: MallBuyGoods, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchMallIntegralInfo(data) {
    try {
      const optons = { url: MallIntegralInfo, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchMallStoreSlides(data) {
    try {
      const optons = { url: MallStoreSlides, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchMallStoreIndex(data) {
    try {
      const optons = { url: MallStoreIndex, data }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
}

const mallHttpInteractor = new MallHttpInteractor(Request.getInstance())
export default mallHttpInteractor
