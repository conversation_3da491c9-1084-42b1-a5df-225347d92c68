import { Request } from '@/core/services/http/request'
import { Login, ForgotPassword, SendCode, ULogin, WechatLogin } from '@/constants/api/login'

class LoginHttpInteractor {
  service
  constructor(service) {
    this.service = service
  }
  async sendSmsCode(params) {
    try {
      const optons = { url: SendCode, params }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async resetForgotPassword(params) {
    try {
      const optons = { url: ForgotPassword, params }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async userLogin(params) {
    try {
      const optons = { url: ULogin, params }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async fetchLogin(params) {
    try {
      const optons = { url: Login, params }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async wechatLogin(params) {
    try {
      const optons = { url: WechatLog<PERSON>, params }
      return await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async getLogin() {
    try {
      const options = { url: Login }
      return await this.service.get(options)
    } catch (error) {
      throw error
    }
  }
  async createLogin(data) {
    try {
      const optons = { url: Login, data }
      await this.service.post(optons)
    } catch (error) {
      throw error
    }
  }
  async deleteLogin(id) {
    try {
      const options = { url: `$Login/${id}` }
      await this.service.delete(options)
    } catch (error) {
      throw error
    }
  }
  async updateLogin(data, id) {
    try {
      const optons = {
        url: `$Login/${id}`, data }
      await this.service.put(optons)
    } catch (error) {
      throw error
    }
  }
}

const loginHttpInteractor = new LoginHttpInteractor(Request.getInstance())
export default loginHttpInteractor
