import router from '@/router'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import i18n from '@/lang'
import { getToken, cleanLocalStorage } from '@/core/services/cache'
import { TITLE } from '@/constants/settings'
import store from '@/store'

NProgress.configure({ showSpinner: false })
const whiteList = [ '/login', '/forgot', '/inviteDoctor' ] // no redirect whitelist

const getPageTitle = key => {
  const hasKey = i18n.te(`route.${key}`)
  if (hasKey) {
    const pageName = i18n.t(`route.${key}`)
    return `${pageName} - ${TITLE}`
  }
  return `${TITLE}`
}

router.beforeEach(async(to, from, next) => {
  NProgress.start()
  next()
  const hasToken = getToken()
  // console.log(hasToken)
  // if (!hasToken) {
  //   console.log('login')
  //   next(`/login?redirect=${to.path}`)
  //   NProgress.done()
  // }
  if (hasToken) {
    if (to.path === '/login') {
      // if is logged in, redirect to the home page
      next({ path: '/' })
      NProgress.done()
    } else {

    }
    store.dispatch('user/refreshUserInfo', '')
    next()
  } else {
    /* has no token*/
    cleanLocalStorage('info')
    if (whiteList.indexOf(to.path) !== -1) {
      // in the free login whitelist, go directly
      console.log('whiteList')
      next()
    } else {
      // other pages that do not have permission to access are redirected to the login page.
      console.log('login')
      next(`/login?redirect=${to.path}`)
      NProgress.done()
    }
  }
})

router.afterEach((to) => {
  NProgress.done()
  document.title = to.meta.title
})
