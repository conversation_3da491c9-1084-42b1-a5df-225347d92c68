import { getLocale } from '@/lang'
import { setLanguage } from '@/core/services/cache'

const state = {
  language: getLocale()
}

const mutations = {
  SET_LANGUAGE(state, language) {
    state.language = language
    setLanguage(language)
  },
  SET_NEW_SYSMESSAGE_COUNT(state, count) {
    state.new_sysmessage_count = count
  }
}

const actions = {
  SetLanguage({ commit }, language) {
    commit('SET_LANGUAGE', language)
  },
  SetSysmessageCount({ commit }, count) {
    commit('SET_NEW_SYSMESSAGE_COUNT', count)
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
