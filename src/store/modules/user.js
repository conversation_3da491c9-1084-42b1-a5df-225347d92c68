import { getToken, removeToken, saveToLocalStorage } from '@/core/services/cache'
import { indexInteractor } from '@/core'
import context from '../../main.js'

const state = {
  token: getToken()
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_USERINFO: (state, userInfo) => {
    state.userInfo = userInfo
  },
}

const actions = {
  getInfo({ commit, state }) {

  },

  logout({ commit, state, dispatch }) {
    commit('SET_TOKEN', '')
    removeToken()
  },

  resetToken({ commit }) {
    return new Promise(resolve => {
      commit('SET_TOKEN', '')
      removeToken()
      resolve()
    })
  },
  // 刷新用户信息
  refreshUserInfo({ commit }, p) {
    return new Promise((resolve, reject) => {
      indexInteractor.getPromoterInfo().then(res => {
        // 存用户信息，token
        // console.log(res)
        context.$bus.emit('refreshUserInfo', res)
        commit('SET_USERINFO', saveToLocalStorage(res))
        resolve(res)
      }).catch(error => {
        console.log(error)
        reject(error)
      })
    })
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
