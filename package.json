{"name": "xlz-sales", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "start": "npm run serve", "build:prod": "vue-cli-service build && rm -rf dist/static/js/*.js.map", "build:stage": "vue-cli-service build --mode staging", "test:unit": "vue-cli-service test:unit", "lint": "vue-cli-service lint", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "new": "plop", "genlog": "conventional-changelog -p angular -i .github/CHANGELOG.md -s", "git-cz": "git add . && npx git-cz"}, "dependencies": {"@sentry/browser": "^5.13.0", "@sentry/integrations": "^5.13.0", "async-validator": "^3.2.3", "axios": "^0.19.2", "compression-webpack-plugin": "^4.0.0", "core-js": "^3.6.4", "dayjs": "^1.8.24", "dsbridge": "^3.1.4", "good-storage": "^1.1.1", "idb": "^5.0.1", "js-cookie": "^2.2.1", "lodash": "^4.17.15", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "qs": "^6.9.1", "register-service-worker": "^1.6.2", "vant": "^2.12.25", "vconsole": "^3.3.4", "vue": "^2.6.11", "vue-i18n": "^8.15.4", "vue-mobile-audio": "^0.1.3", "vue-qriously": "^1.1.1", "vue-router": "^3.1.5", "vuex": "^3.1.2", "weixin-js-sdk": "^1.4.0-test"}, "devDependencies": {"@sentry/webpack-plugin": "^1.10.0", "@vue/cli-plugin-babel": "^4.2.0", "@vue/cli-plugin-eslint": "^4.2.0", "@vue/cli-plugin-pwa": "^4.2.0", "@vue/cli-plugin-router": "^4.2.0", "@vue/cli-plugin-unit-mocha": "^4.2.0", "@vue/cli-plugin-vuex": "^4.2.0", "@vue/cli-service": "^4.2.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/test-utils": "1.0.0-beta.31", "babel-eslint": "^10.0.3", "babel-plugin-try-catch-error-report": "^0.1.0", "babel-polyfill": "^6.26.0", "chai": "^4.1.2", "commitizen": "^4.0.4", "conventional-changelog": "^3.1.18", "cz-conventional-changelog": "^3.1.0", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.1", "eslint-plugin-vue": "^6.1.2", "lodash-webpack-plugin": "^0.11.5", "plop": "^2.6.0", "postcss-px-to-viewport": "^1.1.1", "prettier": "^1.19.1", "sass": "^1.25.0", "sass-loader": "^8.0.2", "script-ext-html-webpack-plugin": "^2.1.4", "style-resources-loader": "^1.3.3", "svg-sprite-loader": "^4.2.1", "svgo": "^1.3.2", "uglifyjs-webpack-plugin": "^2.2.0", "vconsole-webpack-plugin": "^1.5.1", "vue-cli-plugin-style-resources-loader": "^0.1.4", "vue-easytable": "^1.7.2", "vue-template-compiler": "^2.6.11"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended", "@vue/prettier"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}, "overrides": [{"files": ["**/__tests__/*.{j,t}s?(x)", "**/tests/unit/**/*.spec.{j,t}s?(x)"], "env": {"mocha": true}}]}, "browserslist": ["ie 11"], "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}